<?php
require_once 'includes/functions.php';

// Get category slug from URL
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: index.php');
    exit();
}

// Get category data
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM categories WHERE slug = ?");
$stmt->execute([$slug]);
$category = $stmt->fetch();

if (!$category) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit();
}

// Pagination settings
$posts_per_page = 6;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $posts_per_page;

// Get posts for this category
$posts = getPosts($posts_per_page, $offset, $category['id']);
$total_posts = getTotalPosts($category['id']);
$total_pages = ceil($total_posts / $posts_per_page);

// Page settings
$page_title = $category['name'] . ' - Category';
$page_description = $category['description'] ?: 'Browse all posts in the ' . $category['name'] . ' category.';
$page_keywords = $category['name'] . ', blog, articles, category';

// Get data for sidebar
$categories = getCategories();
$popular_posts = getPopularPosts(3);
$recent_posts = getRecentPosts(3);

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1><?php echo htmlspecialchars($category['name']); ?></h1>
        <p><?php echo $category['description'] ? htmlspecialchars($category['description']) : 'Browse all posts in the ' . htmlspecialchars($category['name']) . ' category.'; ?></p>
    </div>
</section>

<!-- Main Content -->
<section class="main-content container">
    <!-- Blog Posts -->
    <main class="blog-posts">
        <?php if (empty($posts)): ?>
            <div class="no-posts">
                <h2>No posts found</h2>
                <p>There are no published posts in this category at the moment. Please check back later!</p>
                <a href="index.php" class="btn btn-primary">Back to Home</a>
            </div>
        <?php else: ?>
            <div class="category-info" style="margin-bottom: 2rem; padding: 1.5rem; background: var(--white); border-radius: 8px; box-shadow: var(--shadow);">
                <h2>Posts in "<?php echo htmlspecialchars($category['name']); ?>" (<?php echo $total_posts; ?>)</h2>
                <?php if ($category['description']): ?>
                    <p><?php echo htmlspecialchars($category['description']); ?></p>
                <?php endif; ?>
            </div>
            
            <?php foreach ($posts as $post): ?>
                <article class="post-card">
                    <div class="post-image">
                        <?php if ($post['featured_image']): ?>
                            <img src="<?php echo $post['featured_image']; ?>" alt="<?php echo htmlspecialchars($post['title']); ?>">
                        <?php else: ?>
                            <img src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="<?php echo htmlspecialchars($post['title']); ?>">
                        <?php endif; ?>
                    </div>
                    <div class="post-content">
                        <div class="post-meta">
                            <span><i class="far fa-calendar"></i> <?php echo formatDate($post['created_at']); ?></span>
                            <span><i class="far fa-user"></i> <?php echo htmlspecialchars($post['author_name']); ?></span>
                            <span><i class="far fa-eye"></i> <?php echo $post['views']; ?> views</span>
                        </div>
                        
                        <h2 class="post-title">
                            <a href="post.php?slug=<?php echo $post['slug']; ?>"><?php echo htmlspecialchars($post['title']); ?></a>
                        </h2>
                        
                        <p class="post-excerpt">
                            <?php echo $post['excerpt'] ? htmlspecialchars($post['excerpt']) : truncateText(strip_tags($post['content']), 150); ?>
                        </p>
                        
                        <a href="post.php?slug=<?php echo $post['slug']; ?>" class="read-more">
                            Read More <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($current_page > 1): ?>
                        <a href="?slug=<?php echo $slug; ?>&page=<?php echo $current_page - 1; ?>"><i class="fas fa-chevron-left"></i></a>
                    <?php endif; ?>
                    
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <?php if ($i == $current_page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?slug=<?php echo $slug; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($current_page < $total_pages): ?>
                        <a href="?slug=<?php echo $slug; ?>&page=<?php echo $current_page + 1; ?>"><i class="fas fa-chevron-right"></i></a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>
    
    <!-- Sidebar -->
    <aside class="sidebar">
        <!-- Search Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Search Blog</h3>
            <form class="search-form" action="search.php" method="GET">
                <input type="text" name="q" placeholder="Search articles...">
                <button type="submit"><i class="fas fa-search"></i></button>
            </form>
        </div>
        
        <!-- Categories Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">All Categories</h3>
            <ul class="categories-list">
                <?php foreach ($categories as $cat): ?>
                    <li>
                        <a href="category.php?slug=<?php echo $cat['slug']; ?>" <?php echo ($cat['id'] == $category['id']) ? 'style="color: var(--primary); font-weight: 600;"' : ''; ?>>
                            <?php echo htmlspecialchars($cat['name']); ?>
                        </a>
                        <span class="category-count"><?php echo $cat['post_count']; ?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- Popular Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Popular Posts</h3>
            <?php foreach ($popular_posts as $popular_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($popular_post['featured_image']): ?>
                            <img src="<?php echo $popular_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($popular_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $popular_post['slug']; ?>"><?php echo htmlspecialchars($popular_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($popular_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Recent Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Recent Posts</h3>
            <?php foreach ($recent_posts as $recent_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($recent_post['featured_image']): ?>
                            <img src="<?php echo $recent_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($recent_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $recent_post['slug']; ?>"><?php echo htmlspecialchars($recent_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($recent_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </aside>
</section>

<?php include 'includes/footer.php'; ?>
