<?php
require_once 'includes/functions.php';

// Get post slug from URL
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: index.php');
    exit();
}

// Get post data
$post = getPostBySlug($slug);

if (!$post) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit();
}

// Increment post views
incrementViews($post['id']);

// Get post tags
$tags = getPostTags($post['id']);

// Get comments
$comments = getComments($post['id']);

// Handle comment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_comment'])) {
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $comment = sanitize($_POST['comment']);
    
    if (!empty($name) && !empty($email) && !empty($comment)) {
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            if (addComment($post['id'], $name, $email, $comment)) {
                $success_message = "Your comment has been submitted and is awaiting approval.";
            } else {
                $error_message = "There was an error submitting your comment. Please try again.";
            }
        } else {
            $error_message = "Please enter a valid email address.";
        }
    } else {
        $error_message = "All fields are required.";
    }
}

// Page settings
$page_title = $post['title'];
$page_description = $post['excerpt'] ? $post['excerpt'] : truncateText(strip_tags($post['content']), 160);
$page_keywords = $post['category_name'] . ', blog, article';
$page_image = $post['featured_image'] ?: 'assets/images/default-og-image.jpg';

// Get data for sidebar
$categories = getCategories();
$popular_posts = getPopularPosts(3);
$recent_posts = getRecentPosts(3);

include 'includes/header.php';
?>

<!-- Main Content -->
<section class="main-content container" style="padding-top: 6rem;">
    <!-- Blog Post -->
    <main class="blog-posts">
        <article class="single-post">
            <?php if ($post['featured_image']): ?>
                <div class="single-post-image">
                    <img src="<?php echo $post['featured_image']; ?>" alt="<?php echo htmlspecialchars($post['title']); ?>">
                </div>
            <?php endif; ?>
            
            <div class="single-post-content">
                <div class="post-meta">
                    <span><i class="far fa-calendar"></i> <?php echo formatDate($post['created_at']); ?></span>
                    <span><i class="far fa-user"></i> <?php echo htmlspecialchars($post['author_name']); ?></span>
                    <span><i class="far fa-eye"></i> <?php echo $post['views']; ?> views</span>
                    <?php if ($post['category_name']): ?>
                        <span><i class="far fa-folder"></i> <a href="category.php?slug=<?php echo $post['category_slug']; ?>"><?php echo htmlspecialchars($post['category_name']); ?></a></span>
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($tags)): ?>
                    <div class="post-tags">
                        <?php foreach ($tags as $tag): ?>
                            <a href="search.php?tag=<?php echo $tag['slug']; ?>" class="tag"><?php echo htmlspecialchars($tag['name']); ?></a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <h1><?php echo htmlspecialchars($post['title']); ?></h1>
                
                <div class="post-content">
                    <?php echo nl2br($post['content']); ?>
                </div>
                
                <!-- Share Buttons -->
                <div class="share-buttons" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--light-gray);">
                    <h4>Share this post:</h4>
                    <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                        <button onclick="sharePost('facebook', '<?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>', '<?php echo addslashes($post['title']); ?>')" class="btn btn-primary">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </button>
                        <button onclick="sharePost('twitter', '<?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>', '<?php echo addslashes($post['title']); ?>')" class="btn btn-primary">
                            <i class="fab fa-twitter"></i> Twitter
                        </button>
                        <button onclick="sharePost('linkedin', '<?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>', '<?php echo addslashes($post['title']); ?>')" class="btn btn-primary">
                            <i class="fab fa-linkedin-in"></i> LinkedIn
                        </button>
                        <button onclick="copyToClipboard('<?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>')" class="btn btn-secondary">
                            <i class="fas fa-link"></i> Copy Link
                        </button>
                    </div>
                </div>
            </div>
        </article>
        
        <!-- Comments Section -->
        <div class="comments-section">
            <h3>Comments (<?php echo count($comments); ?>)</h3>
            
            <?php if (!empty($comments)): ?>
                <?php foreach ($comments as $comment): ?>
                    <div class="comment">
                        <div class="comment-header">
                            <span class="comment-author"><?php echo htmlspecialchars($comment['name']); ?></span>
                            <span class="comment-date"><?php echo formatDate($comment['created_at'], 'F j, Y \a\t g:i A'); ?></span>
                        </div>
                        <div class="comment-content">
                            <?php echo nl2br(htmlspecialchars($comment['comment'])); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p>No comments yet. Be the first to comment!</p>
            <?php endif; ?>
        </div>
        
        <!-- Comment Form -->
        <div class="comment-form">
            <h3>Leave a Comment</h3>
            
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success" style="background: #d4edda; color: #155724; padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-error" style="background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="name">Name *</label>
                    <input type="text" id="name" name="name" required value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" name="email" required value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="comment">Comment *</label>
                    <textarea id="comment" name="comment" required><?php echo isset($_POST['comment']) ? htmlspecialchars($_POST['comment']) : ''; ?></textarea>
                </div>
                
                <button type="submit" name="submit_comment" class="btn btn-primary">Submit Comment</button>
            </form>
        </div>
    </main>
    
    <!-- Sidebar -->
    <aside class="sidebar">
        <!-- Search Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Search Blog</h3>
            <form class="search-form" action="search.php" method="GET">
                <input type="text" name="q" placeholder="Search articles...">
                <button type="submit"><i class="fas fa-search"></i></button>
            </form>
        </div>
        
        <!-- Categories Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Categories</h3>
            <ul class="categories-list">
                <?php foreach ($categories as $category): ?>
                    <li>
                        <a href="category.php?slug=<?php echo $category['slug']; ?>"><?php echo htmlspecialchars($category['name']); ?></a>
                        <span class="category-count"><?php echo $category['post_count']; ?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- Popular Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Popular Posts</h3>
            <?php foreach ($popular_posts as $popular_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($popular_post['featured_image']): ?>
                            <img src="<?php echo $popular_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($popular_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $popular_post['slug']; ?>"><?php echo htmlspecialchars($popular_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($popular_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Recent Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Recent Posts</h3>
            <?php foreach ($recent_posts as $recent_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($recent_post['featured_image']): ?>
                            <img src="<?php echo $recent_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($recent_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $recent_post['slug']; ?>"><?php echo htmlspecialchars($recent_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($recent_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </aside>
</section>

<?php include 'includes/footer.php'; ?>
