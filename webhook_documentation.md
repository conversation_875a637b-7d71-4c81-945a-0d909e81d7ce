
# 📘 Webhook Authentication & Blog Post Creation API Documentation

## 🔗 Webhook URL Format

```
POST http://localhost/your-folder/hhsjfbbshbfsgsgefgu.php?ggg=efhkshkjshrrgh&isif=734-bdfd-7sbf3-vdv01&qwen=023987576892357734-7583
```

---

## 📥 Required POST Data (JSON)

### ✅ Minimum Required Fields:

```json
{
  "title": "Your Blog Post Title",
  "content": "Your full blog post content here..."
}
```

### 🧩 Complete Example with All Options:

```json
{
  "title": "Amazing Blog Post Title",
  "content": "This is the full content of your blog post. It can be very long and include HTML formatting if needed.",
  "excerpt": "Brief description of the post (optional - auto-generated if not provided)",
  "category": "technology",
  "tags": ["web development", "php", "automation"],
  "featured_image": "https://example.com/image.jpg",
  "status": "published",
  "author_id": 1,
  "slug": "custom-url-slug",
  "meta_description": "SEO meta description",
  "meta_keywords": "seo, keywords, blog"
}
```

---

## 🧾 Field Descriptions

| Field             | Type     | Required | Description                                                  |
|------------------|----------|----------|--------------------------------------------------------------|
| `title`          | string   | ✅ Yes   | Blog post title                                              |
| `content`        | string   | ✅ Yes   | Full blog post content (can include HTML)                    |
| `excerpt`        | string   | ❌ No    | Brief description (auto-generated if empty)                  |
| `category`       | string   | ❌ No    | Category slug (creates category if doesn't exist)            |
| `tags`           | array    | ❌ No    | Array of tag names                                           |
| `featured_image` | string   | ❌ No    | URL to featured image                                        |
| `status`         | string   | ❌ No    | "published" or "draft" (default: "published")                |
| `author_id`      | integer  | ❌ No    | User ID (default: 1 - admin)                                 |
| `slug`           | string   | ❌ No    | Custom URL slug (auto-generated if empty)                    |
| `meta_description`| string  | ❌ No    | SEO meta description                                         |
| `meta_keywords`  | string   | ❌ No    | SEO keywords                                                 |

---

## ⚙️ n8n Webhook Configuration

### 1. HTTP Request Node Settings:

- **Method:** `POST`  
- **URL:**  
  ```
  http://your-domain/hhsjfbbshbfsgsgefgu.php?ggg=efhkshkjshrrgh&isif=734-bdfd-7sbf3-vdv01&qwen=023987576892357734-7583
  ```
- **Headers:**
  ```
  Content-Type: application/json
  ```

### 2. Example n8n JSON Body:

```json
{
  "title": "{{ $json.title }}",
  "content": "{{ $json.content }}",
  "excerpt": "{{ $json.excerpt }}",
  "category": "{{ $json.category }}",
  "tags": {{ $json.tags }},
  "featured_image": "{{ $json.featured_image }}",
  "status": "published"
}
```

---

## 📝 Response Format

### ✅ Success Response (201):

```json
{
  "success": true,
  "message": "Blog post created successfully",
  "data": {
    "post_id": 123,
    "title": "Your Post Title",
    "slug": "your-post-title",
    "status": "published",
    "category": {
      "id": 1,
      "name": "Technology",
      "slug": "technology"
    },
    "author": {
      "id": 1,
      "name": "admin"
    },
    "tags": [
      {"name": "web development", "slug": "web-development"},
      {"name": "php", "slug": "php"}
    ],
    "featured_image": "https://example.com/image.jpg",
    "excerpt": "Brief description...",
    "created_at": "2024-01-15 10:30:00",
    "post_url": "http://your-domain/post.php?slug=your-post-title"
  },
  "timestamp": "2024-01-15 10:30:00"
}
```

### ❌ Error Response (400/401/500):

```json
{
  "success": false,
  "message": "Error description",
  "data": null,
  "timestamp": "2024-01-15 10:30:00"
}
```

---

## 🔒 Security Features

- **Authentication:** Three-parameter validation  
- **Method Restriction:** Only `POST` requests allowed  
- **Input Validation:** Validates all required fields  
- **SQL Injection Protection:** Uses prepared statements  
- **Logging:** All requests logged to `webhook_log.txt`  
- **Error Handling:** Comprehensive error responses  

---

## 🛠 Smart Features

- **Auto-slug Generation:** Automatically creates URL-friendly slugs  
- **Duplicate Slug Handling:** Adds suffix to make slugs unique  
- **Auto-excerpt:** Generates excerpt if not provided  
- **Category Creation:** Auto-creates category if not exists  
- **Tag Management:** Auto-creates and links tags  
- **Author Validation:** Defaults to admin if user not found  

---

## 🧪 Testing the Webhook

You can test the webhook using `curl`:

```bash
curl -X POST "http://localhost/your-folder/hhsjfbbshbfsgsgefgu.php?ggg=efhkshkjshrrgh&isif=734-bdfd-7sbf3-vdv01&qwen=023987576892357734-7583" \
-H "Content-Type: application/json" \
-d '{
  "title": "Test Post from Automation",
  "content": "This is a test post created via webhook automation.",
  "category": "automation",
  "tags": ["test", "webhook", "automation"]
}'
```

---

## 📋 Error Codes

| Code | Description                                  |
|------|----------------------------------------------|
| 200  | Success                                      |
| 400  | Bad Request (missing fields, invalid JSON)   |
| 401  | Unauthorized (authentication failed)         |
| 405  | Method Not Allowed (only POST allowed)       |
| 500  | Server Error (e.g., database issues)         |

---

## 🎉 Your webhook is now ready for **n8n** automation!  
It will securely create blog posts with all the features you need. 🚀
