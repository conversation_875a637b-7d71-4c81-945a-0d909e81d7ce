<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | MedBookRent Blog</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5cc5;
            --primary-dark: #1a3d8f;
            --secondary: #4caf50;
            --accent: #ff9800;
            --dark: #2c3e50;
            --light: #f8f9fa;
            --light-gray: #e9ecef;
            --medium-gray: #ced4da;
            --gray: #6c757d;
            --white: #ffffff;
            --sidebar-bg: #1a2a4f;
            --sidebar-hover: #24385f;
            --shadow: 0 4px 12px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background-color: #f5f7fa;
            overflow-x: hidden;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* Admin Layout */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .admin-sidebar {
            width: 250px;
            background: var(--sidebar-bg);
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            transition: var(--transition);
            z-index: 100;
        }
        
        .admin-logo {
            display: flex;
            align-items: center;
            padding: 0 1.5rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .admin-logo i {
            font-size: 1.8rem;
            margin-right: 0.8rem;
            color: var(--accent);
        }
        
        .admin-logo h1 {
            font-size: 1.4rem;
        }
        
        .admin-menu {
            margin-top: 2rem;
        }
        
        .admin-menu ul {
            list-style: none;
        }
        
        .admin-menu li {
            margin-bottom: 0.3rem;
        }
        
        .admin-menu a {
            display: flex;
            align-items: center;
            padding: 0.9rem 1.5rem;
            color: #ddd;
            text-decoration: none;
            transition: var(--transition);
            font-size: 1rem;
        }
        
        .admin-menu a:hover, 
        .admin-menu a.active {
            background: var(--sidebar-hover);
            color: white;
            border-left: 4px solid var(--accent);
        }
        
        .admin-menu i {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
        }
        
        /* Main Content */
        .admin-main {
            flex: 1;
            margin-left: 250px;
            transition: var(--transition);
        }
        
        /* Top Navigation */
        .admin-topbar {
            background: var(--white);
            box-shadow: var(--shadow);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 90;
        }
        
        .toggle-sidebar {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--dark);
            cursor: pointer;
            display: none;
        }
        
        .admin-search {
            position: relative;
            width: 300px;
        }
        
        .admin-search input {
            width: 100%;
            padding: 0.7rem 1rem 0.7rem 3rem;
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .admin-search i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
        }
        
        .admin-user {
            display: flex;
            align-items: center;
        }
        
        .user-info {
            margin-right: 1rem;
            text-align: right;
        }
        
        .user-info h4 {
            font-size: 0.9rem;
        }
        
        .user-info p {
            font-size: 0.8rem;
            color: var(--gray);
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        /* Content Area */
        .admin-content {
            padding: 2rem;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 1.8rem;
            color: var(--primary-dark);
            position: relative;
            padding-bottom: 0.5rem;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--accent);
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: var(--white);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-right: 1.5rem;
        }
        
        .stat-info h3 {
            font-size: 1.8rem;
            margin-bottom: 0.2rem;
        }
        
        .stat-info p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        .icon-blog { background: rgba(44, 92, 197, 0.1); color: var(--primary); }
        .icon-users { background: rgba(76, 175, 80, 0.1); color: var(--secondary); }
        .icon-comments { background: rgba(255, 152, 0, 0.1); color: var(--accent); }
        .icon-pending { background: rgba(108, 117, 125, 0.1); color: var(--gray); }
        
        /* Form Styles */
        .form-container {
            background: var(--white);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        
        .form-row {
            margin-bottom: 1.5rem;
        }
        
        .form-row label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-row input,
        .form-row textarea,
        .form-row select {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
        }
        
        .form-row textarea {
            min-height: 150px;
            resize: vertical;
        }
        
        .form-row input:focus,
        .form-row textarea:focus,
        .form-row select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(44, 92, 197, 0.1);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 4px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .btn-accent {
            background: var(--accent);
            color: white;
        }
        
        .btn-accent:hover {
            background: #e68a00;
        }
        
        .btn-secondary {
            background: var(--light-gray);
            color: var(--dark);
        }
        
        .btn-secondary:hover {
            background: #d8dde3;
        }
        
        .btn-group {
            display: flex;
            gap: 1rem;
        }
        
        /* Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--white);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        
        .data-table th {
            background: var(--primary);
            color: white;
            text-align: left;
            padding: 1rem;
            font-weight: 500;
        }
        
        .data-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background-color: rgba(44, 92, 197, 0.03);
        }
        
        .status-published {
            background: rgba(76, 175, 80, 0.1);
            color: var(--secondary);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            display: inline-block;
        }
        
        .status-draft {
            background: rgba(108, 117, 125, 0.1);
            color: var(--gray);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            display: inline-block;
        }
        
        .table-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .action-btn {
            width: 35px;
            height: 35px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .action-edit {
            background: rgba(44, 92, 197, 0.1);
            color: var(--primary);
        }
        
        .action-edit:hover {
            background: rgba(44, 92, 197, 0.2);
        }
        
        .action-delete {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        
        .action-delete:hover {
            background: rgba(220, 53, 69, 0.2);
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .admin-sidebar {
                width: 70px;
            }
            
            .admin-sidebar .admin-logo h1,
            .admin-sidebar .menu-text {
                display: none;
            }
            
            .admin-sidebar .admin-logo {
                justify-content: center;
                padding: 0 0 1.5rem;
            }
            
            .admin-sidebar .admin-logo i {
                margin-right: 0;
            }
            
            .admin-menu a {
                justify-content: center;
                padding: 0.9rem;
            }
            
            .admin-menu i {
                margin-right: 0;
            }
            
            .admin-main {
                margin-left: 70px;
            }
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
            }
            
            .admin-sidebar.active {
                transform: translateX(0);
            }
            
            .admin-main {
                margin-left: 0;
            }
            
            .toggle-sidebar {
                display: block;
            }
            
            .admin-search {
                display: none;
            }
            
            .admin-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Login Page */
        .login-container {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(to right, rgba(44, 92, 197, 0.8), rgba(26, 61, 143, 0.8)), url('https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
            background-size: cover;
            background-position: center;
            justify-content: center;
            align-items: center;
            padding: 1rem;
        }
        
        .login-box {
            background: var(--white);
            border-radius: 8px;
            box-shadow: var(--shadow);
            width: 100%;
            max-width: 450px;
            padding: 2.5rem;
            text-align: center;
        }
        
        .login-logo {
            margin-bottom: 2rem;
            color: var(--primary);
        }
        
        .login-logo i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .login-logo h1 {
            font-size: 1.8rem;
        }
        
        .login-form .form-row {
            margin-bottom: 1.5rem;
            text-align: left;
        }
        
        .login-form input {
            padding: 0.9rem 1.2rem;
        }
        
        .btn-login {
            width: 100%;
            padding: 1rem;
            font-size: 1rem;
            margin-top: 0.5rem;
        }
        
        .admin-footer {
            text-align: center;
            padding: 1rem;
            color: var(--gray);
            font-size: 0.9rem;
            border-top: 1px solid var(--light-gray);
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <!-- Admin Login Page -->
    <div class="login-container" id="login-page">
        <div class="login-box">
            <div class="login-logo">
                <i class="fas fa-book-medical"></i>
                <h1>MedBookRent Admin</h1>
            </div>
            <form class="login-form">
                <div class="form-row">
                    <label for="username">Username</label>
                    <input type="text" id="username" placeholder="Enter your username">
                </div>
                <div class="form-row">
                    <label for="password">Password</label>
                    <input type="password" id="password" placeholder="Enter your password">
                </div>
                <button type="button" class="btn btn-primary btn-login" onclick="login()">Login to Dashboard</button>
            </form>
            <div class="admin-footer">
                <p>© 2023 MedBookRent. Admin Access Only</p>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div class="admin-container" id="admin-dashboard" style="display: none;">
        <!-- Sidebar -->
        <div class="admin-sidebar" id="sidebar">
            <div class="admin-logo">
                <i class="fas fa-book-medical"></i>
                <h1>MedBookRent</h1>
            </div>
            <nav class="admin-menu">
                <ul>
                    <li><a href="#" class="active"><i class="fas fa-home"></i> <span class="menu-text">Dashboard</span></a></li>
                    <li><a href="#"><i class="fas fa-book"></i> <span class="menu-text">Blog Posts</span></a></li>
                    <li><a href="#"><i class="fas fa-plus-circle"></i> <span class="menu-text">Add New Post</span></a></li>
                    <li><a href="#"><i class="fas fa-folder"></i> <span class="menu-text">Categories</span></a></li>
                    <li><a href="#"><i class="fas fa-tags"></i> <span class="menu-text">Tags</span></a></li>
                    <li><a href="#"><i class="fas fa-comments"></i> <span class="menu-text">Comments</span></a></li>
                    <li><a href="#"><i class="fas fa-users"></i> <span class="menu-text">Users</span></a></li>
                    <li><a href="#"><i class="fas fa-cog"></i> <span class="menu-text">Settings</span></a></li>
                    <li><a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> <span class="menu-text">Logout</span></a></li>
                </ul>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="admin-main">
            <!-- Top Navigation -->
            <div class="admin-topbar">
                <button class="toggle-sidebar" id="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="admin-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search...">
                </div>
                <div class="admin-user">
                    <div class="user-info">
                        <h4>Admin User</h4>
                        <p>Administrator</p>
                    </div>
                    <div class="user-avatar">
                        AU
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="admin-content">
                <div class="admin-header">
                    <h1 class="page-title">Dashboard</h1>
                    <div class="btn-group">
                        <button class="btn btn-accent"><i class="fas fa-plus"></i> Add New Post</button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon icon-blog">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-info">
                            <h3>42</h3>
                            <p>Blog Posts</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon icon-users">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>128</h3>
                            <p>Registered Users</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon icon-comments">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="stat-info">
                            <h3>324</h3>
                            <p>Comments</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon icon-pending">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>5</h3>
                            <p>Pending Posts</p>
                        </div>
                    </div>
                </div>

                <!-- Add New Blog Form -->
                <div class="admin-header">
                    <h1 class="page-title">Add New Blog Post</h1>
                </div>
                
                <div class="form-container">
                    <form id="blog-form">
                        <div class="form-grid">
                            <div class="form-row">
                                <label for="post-title">Post Title</label>
                                <input type="text" id="post-title" placeholder="Enter post title">
                            </div>
                            <div class="form-row">
                                <label for="post-author">Author</label>
                                <input type="text" id="post-author" value="Admin User" readonly>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <label for="post-content">Content</label>
                            <textarea id="post-content" placeholder="Write your blog post content here..."></textarea>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-row">
                                <label for="post-category">Category</label>
                                <select id="post-category">
                                    <option value="">Select a category</option>
                                    <option value="study-tips">Study Tips & Techniques</option>
                                    <option value="textbook-reviews">Textbook Reviews</option>
                                    <option value="exam-prep">Exam Preparation</option>
                                    <option value="resources">Medical Resources</option>
                                    <option value="wellness">Student Wellness</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <label for="post-tags">Tags (comma separated)</label>
                                <input type="text" id="post-tags" placeholder="e.g., NEET, Biology, Study Plan">
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-row">
                                <label for="featured-image">Featured Image URL</label>
                                <input type="text" id="featured-image" placeholder="Paste image URL">
                            </div>
                            <div class="form-row">
                                <label for="post-status">Status</label>
                                <select id="post-status">
                                    <option value="draft">Draft</option>
                                    <option value="published">Published</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="savePost()">Publish Post</button>
                            <button type="button" class="btn btn-secondary">Save Draft</button>
                        </div>
                    </form>
                </div>

                <!-- Recent Blog Posts Table -->
                <div class="admin-header">
                    <h1 class="page-title">Recent Blog Posts</h1>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Author</th>
                            <th>Category</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Top 10 Study Techniques for Medical Students</td>
                            <td>Dr. Ananya Sharma</td>
                            <td>Study Tips</td>
                            <td>Jun 12, 2025</td>
                            <td><span class="status-published">Published</span></td>
                            <td>
                                <div class="table-actions">
                                    <div class="action-btn action-edit"><i class="fas fa-edit"></i></div>
                                    <div class="action-btn action-delete"><i class="fas fa-trash"></i></div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>Trueman's vs Pradeep's Biology Comparison</td>
                            <td>Prof. Rajiv Mehta</td>
                            <td>Textbook Reviews</td>
                            <td>Jun 5, 2025</td>
                            <td><span class="status-published">Published</span></td>
                            <td>
                                <div class="table-actions">
                                    <div class="action-btn action-edit"><i class="fas fa-edit"></i></div>
                                    <div class="action-btn action-delete"><i class="fas fa-trash"></i></div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>NEET Preparation Schedule for 2025</td>
                            <td>Dr. Priya Kapoor</td>
                            <td>Exam Preparation</td>
                            <td>May 28, 2025</td>
                            <td><span class="status-published">Published</span></td>
                            <td>
                                <div class="table-actions">
                                    <div class="action-btn action-edit"><i class="fas fa-edit"></i></div>
                                    <div class="action-btn action-delete"><i class="fas fa-trash"></i></div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>Essential Digital Resources for Medical Students</td>
                            <td>Dr. Arjun Patel</td>
                            <td>Medical Resources</td>
                            <td>May 15, 2025</td>
                            <td><span class="status-published">Published</span></td>
                            <td>
                                <div class="table-actions">
                                    <div class="action-btn action-edit"><i class="fas fa-edit"></i></div>
                                    <div class="action-btn action-delete"><i class="fas fa-trash"></i></div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>Managing Stress During Medical Studies</td>
                            <td>Admin User</td>
                            <td>Student Wellness</td>
                            <td>May 10, 2025</td>
                            <td><span class="status-draft">Draft</span></td>
                            <td>
                                <div class="table-actions">
                                    <div class="action-btn action-edit"><i class="fas fa-edit"></i></div>
                                    <div class="action-btn action-delete"><i class="fas fa-trash"></i></div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Toggle sidebar on mobile
        document.getElementById('toggle-sidebar').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
        });
        
        // Login function
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if(username && password) {
                document.getElementById('login-page').style.display = 'none';
                document.getElementById('admin-dashboard').style.display = 'flex';
            } else {
                alert('Please enter both username and password');
            }
        }
        
        // Logout function
        function logout() {
            document.getElementById('admin-dashboard').style.display = 'none';
            document.getElementById('login-page').style.display = 'flex';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
        }
        
        // Save post function
        function savePost() {
            const title = document.getElementById('post-title').value;
            const category = document.getElementById('post-category').value;
            
            if(title && category) {
                alert('Blog post published successfully!');
                document.getElementById('blog-form').reset();
            } else {
                alert('Please fill in all required fields');
            }
        }
        
        // Initialize some functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add active class to menu items on click
            const menuItems = document.querySelectorAll('.admin-menu a');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    menuItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>