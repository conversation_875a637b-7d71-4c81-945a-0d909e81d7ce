<?php
require_once 'includes/functions.php';

// Page settings
$page_title = 'About Us';
$page_description = 'Learn more about Creative Blog and our mission to share inspiring content.';
$page_keywords = 'about, creative blog, mission, team, story';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>About Creative Blog</h1>
        <p>Discover our story, mission, and the passionate team behind the content you love.</p>
    </div>
</section>

<!-- Main Content -->
<section class="main-content container">
    <div class="about-content">
        <!-- Our Story -->
        <div class="content-section">
            <div class="section-content">
                <h2>Our Story</h2>
                <p>Creative Blog was born from a simple idea: to create a platform where creativity meets knowledge, and where stories inspire action. Founded in 2024, we've grown from a small passion project into a thriving community of writers, readers, and creative minds.</p>
                
                <p>What started as a personal blog has evolved into a comprehensive platform covering technology, lifestyle, travel, health, and education. We believe that everyone has a story to tell and knowledge to share, and our mission is to provide the perfect space for those stories to flourish.</p>
                
                <p>Our journey has been marked by countless late-night writing sessions, endless cups of coffee, and the unwavering belief that quality content can make a difference in people's lives. Today, we're proud to serve thousands of readers who trust us to deliver insightful, engaging, and valuable content.</p>
            </div>
            <div class="section-image">
                <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Our Story">
            </div>
        </div>
        
        <!-- Our Mission -->
        <div class="content-section reverse">
            <div class="section-image">
                <img src="https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Our Mission">
            </div>
            <div class="section-content">
                <h2>Our Mission</h2>
                <p>At Creative Blog, our mission is to inspire, educate, and empower our readers through high-quality content that makes a real difference in their lives. We strive to:</p>
                
                <ul class="mission-list">
                    <li><strong>Inspire Creativity:</strong> Spark new ideas and encourage creative thinking in all aspects of life.</li>
                    <li><strong>Share Knowledge:</strong> Provide valuable insights and practical advice across diverse topics.</li>
                    <li><strong>Build Community:</strong> Foster meaningful connections between readers and writers.</li>
                    <li><strong>Promote Growth:</strong> Support personal and professional development through our content.</li>
                    <li><strong>Embrace Diversity:</strong> Celebrate different perspectives and experiences from around the world.</li>
                </ul>
                
                <p>We believe that knowledge shared is knowledge multiplied, and every article we publish is a step toward building a more informed and creative world.</p>
            </div>
        </div>
        
        <!-- Our Values -->
        <div class="values-section">
            <h2>Our Values</h2>
            <div class="values-grid">
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3>Innovation</h3>
                    <p>We constantly seek new ways to present information and engage with our audience, embracing technology and creative approaches.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Authenticity</h3>
                    <p>Every piece of content reflects genuine experiences and honest perspectives, building trust with our readers.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Community</h3>
                    <p>We foster an inclusive environment where everyone feels welcome to share, learn, and grow together.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>Excellence</h3>
                    <p>We maintain high standards in everything we do, from content quality to user experience and community engagement.</p>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="stats-section">
            <h2>Our Impact</h2>
            <div class="stats-grid">
                <?php
                // Get some statistics
                $total_posts = $pdo->query("SELECT COUNT(*) FROM posts WHERE status = 'published'")->fetchColumn();
                $total_categories = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
                $total_comments = $pdo->query("SELECT COUNT(*) FROM comments WHERE status = 'approved'")->fetchColumn();
                $total_views = $pdo->query("SELECT SUM(views) FROM posts")->fetchColumn() ?: 0;
                ?>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($total_posts); ?></div>
                    <div class="stat-label">Articles Published</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($total_views); ?></div>
                    <div class="stat-label">Total Views</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($total_categories); ?></div>
                    <div class="stat-label">Categories</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($total_comments); ?></div>
                    <div class="stat-label">Community Comments</div>
                </div>
            </div>
        </div>
        
        <!-- Contact CTA -->
        <div class="contact-cta">
            <h2>Get In Touch</h2>
            <p>Have a story to share? Want to collaborate? Or just want to say hello? We'd love to hear from you!</p>
            <a href="contact.php" class="cta-button">Contact Us</a>
        </div>
    </div>
</section>

<style>
.about-content {
    max-width: 1000px;
    margin: 0 auto;
}

.content-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 4rem;
    align-items: center;
}

.content-section.reverse {
    direction: rtl;
}

.content-section.reverse > * {
    direction: ltr;
}

.section-content h2 {
    color: var(--primary-dark);
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.section-content p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
    color: var(--dark);
}

.mission-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.mission-list li {
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--light-gray);
    line-height: 1.6;
}

.mission-list li:last-child {
    border-bottom: none;
}

.mission-list strong {
    color: var(--primary);
}

.section-image {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.section-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.values-section {
    margin-bottom: 4rem;
}

.values-section h2 {
    text-align: center;
    color: var(--primary-dark);
    margin-bottom: 2rem;
    font-size: 2rem;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--white);
    font-size: 2rem;
}

.value-card h3 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

.value-card p {
    color: var(--gray);
    line-height: 1.6;
}

.stats-section {
    background: var(--white);
    padding: 3rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 4rem;
}

.stats-section h2 {
    text-align: center;
    color: var(--primary-dark);
    margin-bottom: 2rem;
    font-size: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray);
    font-size: 1.1rem;
}

.contact-cta {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    padding: 3rem;
    border-radius: 8px;
    text-align: center;
}

.contact-cta h2 {
    margin-bottom: 1rem;
}

.contact-cta p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

@media (max-width: 768px) {
    .content-section {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .values-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .section-content h2,
    .values-section h2,
    .stats-section h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
