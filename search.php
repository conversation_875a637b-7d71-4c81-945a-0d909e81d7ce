<?php
require_once 'includes/functions.php';

// Get search query from URL
$search_query = isset($_GET['q']) ? sanitize($_GET['q']) : '';

// Pagination settings
$posts_per_page = 6;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $posts_per_page;

// Get search results
$posts = [];
$total_posts = 0;
$total_pages = 0;

if (!empty($search_query)) {
    $posts = getPosts($posts_per_page, $offset, null, $search_query);
    $total_posts = getTotalPosts(null, $search_query);
    $total_pages = ceil($total_posts / $posts_per_page);
}

// Page settings
$page_title = !empty($search_query) ? 'Search Results for "' . $search_query . '"' : 'Search';
$page_description = !empty($search_query) ? 'Search results for "' . $search_query . '" on Creative Blog.' : 'Search for articles on Creative Blog.';
$page_keywords = 'search, blog, articles, ' . $search_query;

// Get data for sidebar
$categories = getCategories();
$popular_posts = getPopularPosts(3);
$recent_posts = getRecentPosts(3);

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1><?php echo !empty($search_query) ? 'Search Results' : 'Search Blog'; ?></h1>
        <p><?php echo !empty($search_query) ? 'Results for "' . htmlspecialchars($search_query) . '"' : 'Find articles that interest you'; ?></p>
        
        <!-- Search Form -->
        <div style="max-width: 600px; margin: 2rem auto 0;">
            <form class="search-form" method="GET" style="display: flex; background: white; border-radius: 8px; overflow: hidden; box-shadow: var(--shadow);">
                <input type="text" name="q" placeholder="Search articles..." value="<?php echo htmlspecialchars($search_query); ?>" style="flex: 1; padding: 1rem 1.5rem; border: none; font-size: 1.1rem; outline: none;">
                <button type="submit" style="background: var(--primary); color: white; border: none; padding: 1rem 2rem; cursor: pointer; font-size: 1.1rem;">
                    <i class="fas fa-search"></i> Search
                </button>
            </form>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="main-content container">
    <!-- Search Results -->
    <main class="blog-posts">
        <?php if (empty($search_query)): ?>
            <div class="search-info" style="background: var(--white); padding: 2rem; border-radius: 8px; box-shadow: var(--shadow); text-align: center;">
                <h2>Search Our Blog</h2>
                <p>Enter keywords in the search box above to find articles that match your interests.</p>
                
                <div style="margin-top: 2rem;">
                    <h3>Popular Search Terms:</h3>
                    <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; justify-content: center; margin-top: 1rem;">
                        <a href="?q=technology" class="tag">Technology</a>
                        <a href="?q=lifestyle" class="tag">Lifestyle</a>
                        <a href="?q=travel" class="tag">Travel</a>
                        <a href="?q=health" class="tag">Health</a>
                        <a href="?q=education" class="tag">Education</a>
                        <a href="?q=tips" class="tag">Tips</a>
                        <a href="?q=guide" class="tag">Guide</a>
                        <a href="?q=tutorial" class="tag">Tutorial</a>
                    </div>
                </div>
            </div>
        <?php elseif (empty($posts)): ?>
            <div class="no-results" style="background: var(--white); padding: 2rem; border-radius: 8px; box-shadow: var(--shadow); text-align: center;">
                <h2>No Results Found</h2>
                <p>Sorry, no articles were found for "<strong><?php echo htmlspecialchars($search_query); ?></strong>".</p>
                
                <div style="margin-top: 2rem;">
                    <h3>Search Tips:</h3>
                    <ul style="text-align: left; max-width: 400px; margin: 1rem auto;">
                        <li>Check your spelling</li>
                        <li>Try different keywords</li>
                        <li>Use more general terms</li>
                        <li>Try searching for related topics</li>
                    </ul>
                </div>
                
                <a href="index.php" class="btn btn-primary" style="margin-top: 1rem;">Browse All Posts</a>
            </div>
        <?php else: ?>
            <div class="search-results-info" style="margin-bottom: 2rem; padding: 1.5rem; background: var(--white); border-radius: 8px; box-shadow: var(--shadow);">
                <h2>Found <?php echo $total_posts; ?> result<?php echo $total_posts !== 1 ? 's' : ''; ?> for "<?php echo htmlspecialchars($search_query); ?>"</h2>
                <?php if ($total_pages > 1): ?>
                    <p>Showing page <?php echo $current_page; ?> of <?php echo $total_pages; ?></p>
                <?php endif; ?>
            </div>
            
            <?php foreach ($posts as $post): ?>
                <article class="post-card">
                    <div class="post-image">
                        <?php if ($post['featured_image']): ?>
                            <img src="<?php echo $post['featured_image']; ?>" alt="<?php echo htmlspecialchars($post['title']); ?>">
                        <?php else: ?>
                            <img src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="<?php echo htmlspecialchars($post['title']); ?>">
                        <?php endif; ?>
                    </div>
                    <div class="post-content">
                        <div class="post-meta">
                            <span><i class="far fa-calendar"></i> <?php echo formatDate($post['created_at']); ?></span>
                            <span><i class="far fa-user"></i> <?php echo htmlspecialchars($post['author_name']); ?></span>
                            <span><i class="far fa-eye"></i> <?php echo $post['views']; ?> views</span>
                            <?php if ($post['category_name']): ?>
                                <span><i class="far fa-folder"></i> <a href="category.php?slug=<?php echo $post['category_slug']; ?>"><?php echo htmlspecialchars($post['category_name']); ?></a></span>
                            <?php endif; ?>
                        </div>
                        
                        <h2 class="post-title">
                            <a href="post.php?slug=<?php echo $post['slug']; ?>"><?php echo htmlspecialchars($post['title']); ?></a>
                        </h2>
                        
                        <p class="post-excerpt">
                            <?php 
                            $excerpt = $post['excerpt'] ? $post['excerpt'] : strip_tags($post['content']);
                            // Highlight search terms in excerpt
                            if (!empty($search_query)) {
                                $excerpt = preg_replace('/(' . preg_quote($search_query, '/') . ')/i', '<mark style="background: #fff3cd; padding: 0.1rem 0.2rem;">$1</mark>', $excerpt);
                            }
                            echo truncateText($excerpt, 150);
                            ?>
                        </p>
                        
                        <a href="post.php?slug=<?php echo $post['slug']; ?>" class="read-more">
                            Read More <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($current_page > 1): ?>
                        <a href="?q=<?php echo urlencode($search_query); ?>&page=<?php echo $current_page - 1; ?>"><i class="fas fa-chevron-left"></i></a>
                    <?php endif; ?>
                    
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <?php if ($i == $current_page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?q=<?php echo urlencode($search_query); ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($current_page < $total_pages): ?>
                        <a href="?q=<?php echo urlencode($search_query); ?>&page=<?php echo $current_page + 1; ?>"><i class="fas fa-chevron-right"></i></a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>
    
    <!-- Sidebar -->
    <aside class="sidebar">
        <!-- Search Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Refine Search</h3>
            <form class="search-form" method="GET">
                <input type="text" name="q" placeholder="Search articles..." value="<?php echo htmlspecialchars($search_query); ?>">
                <button type="submit"><i class="fas fa-search"></i></button>
            </form>
        </div>
        
        <!-- Categories Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Browse Categories</h3>
            <ul class="categories-list">
                <?php foreach ($categories as $category): ?>
                    <li>
                        <a href="category.php?slug=<?php echo $category['slug']; ?>"><?php echo htmlspecialchars($category['name']); ?></a>
                        <span class="category-count"><?php echo $category['post_count']; ?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- Popular Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Popular Posts</h3>
            <?php foreach ($popular_posts as $popular_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($popular_post['featured_image']): ?>
                            <img src="<?php echo $popular_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($popular_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $popular_post['slug']; ?>"><?php echo htmlspecialchars($popular_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($popular_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Recent Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Recent Posts</h3>
            <?php foreach ($recent_posts as $recent_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($recent_post['featured_image']): ?>
                            <img src="<?php echo $recent_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($recent_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $recent_post['slug']; ?>"><?php echo htmlspecialchars($recent_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($recent_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </aside>
</section>

<?php include 'includes/footer.php'; ?>
