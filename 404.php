<?php
// Page settings
$page_title = '404 - Page Not Found';
$page_description = 'The page you are looking for could not be found.';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>404 - Page Not Found</h1>
        <p>Sorry, the page you are looking for could not be found.</p>
    </div>
</section>

<!-- Main Content -->
<section class="main-content container">
    <div style="text-align: center; padding: 3rem 0;">
        <div style="font-size: 8rem; color: var(--primary); margin-bottom: 1rem;">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h2 style="margin-bottom: 1rem; color: var(--primary-dark);">Oops! Page Not Found</h2>
        
        <p style="margin-bottom: 2rem; color: var(--gray); max-width: 500px; margin-left: auto; margin-right: auto;">
            The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>
        
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home"></i> Go Home
            </a>
            <a href="search.php" class="btn btn-secondary">
                <i class="fas fa-search"></i> Search Blog
            </a>
        </div>
        
        <div style="margin-top: 3rem;">
            <h3 style="margin-bottom: 1rem; color: var(--primary-dark);">Popular Pages</h3>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <?php
                $categories = getCategories();
                $count = 0;
                foreach ($categories as $category):
                    if ($count >= 4) break;
                    $count++;
                ?>
                    <a href="category.php?slug=<?php echo $category['slug']; ?>" class="tag" style="font-size: 1rem; padding: 0.5rem 1rem;">
                        <?php echo htmlspecialchars($category['name']); ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
