<?php
require_once 'config/database.php';

// Sample posts data
$sample_posts = [
    [
        'title' => 'Getting Started with Web Development in 2024',
        'slug' => 'getting-started-web-development-2024',
        'content' => 'Web development has evolved significantly over the years, and 2024 brings exciting new opportunities for developers. In this comprehensive guide, we\'ll explore the essential technologies and frameworks you need to know to start your journey in web development.

HTML, CSS, and JavaScript remain the foundation of web development. However, modern frameworks like React, Vue.js, and Angular have revolutionized how we build interactive user interfaces. On the backend, technologies like Node.js, Python with Django or Flask, and PHP continue to power millions of websites.

The key to success in web development is understanding both the fundamentals and staying updated with the latest trends. Start with the basics, build projects, and never stop learning. The web development community is incredibly supportive, and there are countless resources available to help you on your journey.

Whether you\'re interested in frontend development, backend development, or becoming a full-stack developer, the opportunities are endless. The demand for skilled web developers continues to grow, making it an excellent career choice for 2024 and beyond.',
        'excerpt' => 'Discover the essential technologies and frameworks you need to know to start your web development journey in 2024.',
        'category_id' => 1, // Technology
        'author_id' => 1,
        'status' => 'published',
        'featured_image' => 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ],
    [
        'title' => '10 Healthy Lifestyle Tips for Busy Professionals',
        'slug' => '10-healthy-lifestyle-tips-busy-professionals',
        'content' => 'Maintaining a healthy lifestyle while managing a demanding career can be challenging. However, with the right strategies and mindset, it\'s entirely possible to prioritize your health without sacrificing your professional goals.

1. Start your day with a nutritious breakfast
2. Stay hydrated throughout the day
3. Take regular breaks from your desk
4. Incorporate physical activity into your routine
5. Practice mindful eating
6. Get adequate sleep
7. Manage stress through meditation or yoga
8. Limit screen time before bed
9. Build strong social connections
10. Schedule regular health check-ups

Remember, small changes can lead to significant improvements in your overall well-being. The key is consistency and finding what works best for your lifestyle and schedule.',
        'excerpt' => 'Learn practical tips to maintain a healthy lifestyle while managing a demanding professional career.',
        'category_id' => 4, // Health
        'author_id' => 1,
        'status' => 'published',
        'featured_image' => 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ],
    [
        'title' => 'The Ultimate Guide to Solo Travel: Tips and Destinations',
        'slug' => 'ultimate-guide-solo-travel-tips-destinations',
        'content' => 'Solo travel is one of the most rewarding experiences you can have. It offers complete freedom to explore at your own pace, discover new cultures, and learn more about yourself along the way.

Planning Your Solo Trip:
- Research your destination thoroughly
- Book accommodations in safe areas
- Share your itinerary with someone at home
- Pack light but smart
- Have backup plans

Best Solo Travel Destinations:
1. Japan - Safe, clean, and incredibly welcoming
2. New Zealand - Perfect for adventure seekers
3. Iceland - Stunning landscapes and friendly locals
4. Portugal - Rich culture and affordable
5. Costa Rica - Great for nature lovers

Safety should always be your top priority when traveling alone. Trust your instincts, stay aware of your surroundings, and don\'t be afraid to ask for help when needed. Solo travel can be life-changing, offering experiences and perspectives you\'ll treasure forever.',
        'excerpt' => 'Everything you need to know about solo travel, from planning tips to the best destinations for first-time solo travelers.',
        'category_id' => 3, // Travel
        'author_id' => 1,
        'status' => 'published',
        'featured_image' => 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ],
    [
        'title' => 'Minimalist Living: How to Declutter Your Life and Mind',
        'slug' => 'minimalist-living-declutter-life-mind',
        'content' => 'Minimalism isn\'t just about having fewer possessions; it\'s about making room for what truly matters in your life. By embracing minimalist principles, you can reduce stress, increase focus, and find greater satisfaction in your daily experiences.

The Benefits of Minimalist Living:
- Reduced stress and anxiety
- More time for meaningful activities
- Improved focus and productivity
- Financial freedom through conscious spending
- Environmental benefits

Getting Started with Minimalism:
1. Start small - choose one area to declutter
2. Use the one-year rule - if you haven\'t used it in a year, consider letting it go
3. Focus on experiences over possessions
4. Practice gratitude for what you have
5. Be mindful of new purchases

Remember, minimalism looks different for everyone. The goal isn\'t to live with as few items as possible, but to surround yourself only with things that add value to your life.',
        'excerpt' => 'Discover how minimalist living can help you declutter not just your space, but your mind and life as well.',
        'category_id' => 2, // Lifestyle
        'author_id' => 1,
        'status' => 'published',
        'featured_image' => 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ],
    [
        'title' => 'Online Learning: The Future of Education',
        'slug' => 'online-learning-future-education',
        'content' => 'The landscape of education has been transformed by digital technology. Online learning has evolved from a convenient alternative to a powerful educational tool that offers flexibility, accessibility, and personalized learning experiences.

Advantages of Online Learning:
- Flexibility to learn at your own pace
- Access to courses from top institutions worldwide
- Cost-effective compared to traditional education
- Ability to balance learning with work and family
- Wide variety of subjects and specializations

Popular Online Learning Platforms:
1. Coursera - University-level courses
2. Udemy - Practical skills and hobbies
3. Khan Academy - Free educational content
4. LinkedIn Learning - Professional development
5. MasterClass - Learn from industry experts

Tips for Successful Online Learning:
- Create a dedicated study space
- Set a regular schedule
- Actively participate in discussions
- Take notes and review regularly
- Apply what you learn through projects

The future of education is hybrid, combining the best of online and offline learning experiences.',
        'excerpt' => 'Explore how online learning is revolutionizing education and discover tips for successful digital learning.',
        'category_id' => 5, // Education
        'author_id' => 1,
        'status' => 'published',
        'featured_image' => 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ]
];

try {
    // Insert sample posts
    $stmt = $pdo->prepare("INSERT INTO posts (title, slug, content, excerpt, category_id, author_id, status, featured_image, views) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($sample_posts as $post) {
        $views = rand(50, 500); // Random view count
        $stmt->execute([
            $post['title'],
            $post['slug'],
            $post['content'],
            $post['excerpt'],
            $post['category_id'],
            $post['author_id'],
            $post['status'],
            $post['featured_image'],
            $views
        ]);
    }
    
    // Insert sample comments
    $posts = $pdo->query("SELECT id FROM posts")->fetchAll();
    $sample_comments = [
        ['name' => 'John Doe', 'email' => '<EMAIL>', 'comment' => 'Great article! Very informative and well-written.'],
        ['name' => 'Jane Smith', 'email' => '<EMAIL>', 'comment' => 'Thanks for sharing these tips. I found them really helpful.'],
        ['name' => 'Mike Johnson', 'email' => '<EMAIL>', 'comment' => 'Excellent content. Looking forward to more posts like this.'],
        ['name' => 'Sarah Wilson', 'email' => '<EMAIL>', 'comment' => 'This is exactly what I was looking for. Thank you!'],
        ['name' => 'David Brown', 'email' => '<EMAIL>', 'comment' => 'Well researched and presented. Keep up the good work!']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO comments (post_id, name, email, comment, status) VALUES (?, ?, ?, ?, 'approved')");
    
    foreach ($posts as $post) {
        $num_comments = rand(1, 3);
        for ($i = 0; $i < $num_comments; $i++) {
            $comment = $sample_comments[array_rand($sample_comments)];
            $stmt->execute([
                $post['id'],
                $comment['name'],
                $comment['email'],
                $comment['comment']
            ]);
        }
    }
    
    echo "Sample data installed successfully!<br>";
    echo "You can now visit your blog at <a href='index.php'>index.php</a><br>";
    echo "Admin panel: <a href='admin/login.php'>admin/login.php</a> (admin/admin123)";
    
} catch (PDOException $e) {
    echo "Error installing sample data: " . $e->getMessage();
}
?>
