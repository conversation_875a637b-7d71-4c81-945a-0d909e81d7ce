<?php
// Webhook for automated blog post creation
// This file should be kept secure and the filename should not be shared publicly

header('Content-Type: application/json');
require_once 'config/database.php';
require_once 'includes/functions.php';

// Authentication parameters
$required_ggg = "efhkshkjshrrgh";
$required_isif = "734-bdfd-7sbf3-vdv01";
$required_qwen = "023987576892357734-7583";

// Response function
function sendResponse($success, $message, $data = null, $http_code = 200) {
    http_response_code($http_code);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Log function for debugging
function logWebhook($message, $data = null) {
    $log_entry = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $log_entry .= " - Data: " . json_encode($data);
    }
    $log_entry .= "\n";
    file_put_contents('webhook_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method is allowed', null, 405);
}

// Verify authentication parameters
$ggg = $_GET['ggg'] ?? '';
$isif = $_GET['isif'] ?? '';
$qwen = $_GET['qwen'] ?? '';

if ($ggg !== $required_ggg || $isif !== $required_isif || $qwen !== $required_qwen) {
    logWebhook('Authentication failed', ['ggg' => $ggg, 'isif' => $isif, 'qwen' => $qwen]);
    sendResponse(false, 'Authentication failed', null, 401);
}

// Get POST data
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    sendResponse(false, 'Invalid JSON data', null, 400);
}

// Required fields validation
$required_fields = ['title', 'content'];
$missing_fields = [];

foreach ($required_fields as $field) {
    if (!isset($data[$field]) || empty(trim($data[$field]))) {
        $missing_fields[] = $field;
    }
}

if (!empty($missing_fields)) {
    sendResponse(false, 'Missing required fields: ' . implode(', ', $missing_fields), null, 400);
}

try {
    // Extract and sanitize data
    $title = trim($data['title']);
    $content = trim($data['content']);
    $excerpt = isset($data['excerpt']) ? trim($data['excerpt']) : '';
    $category_slug = isset($data['category']) ? trim($data['category']) : '';
    $tags = isset($data['tags']) ? $data['tags'] : [];
    $featured_image = isset($data['featured_image']) ? trim($data['featured_image']) : '';
    $status = isset($data['status']) ? trim($data['status']) : 'published';
    $author_id = isset($data['author_id']) ? (int)$data['author_id'] : 1; // Default to admin user
    $slug = isset($data['slug']) ? trim($data['slug']) : '';
    $meta_description = isset($data['meta_description']) ? trim($data['meta_description']) : '';
    $meta_keywords = isset($data['meta_keywords']) ? trim($data['meta_keywords']) : '';
    
    // Generate slug if not provided
    if (empty($slug)) {
        $slug = createSlug($title);
    } else {
        $slug = createSlug($slug);
    }
    
    // Check if slug already exists and make it unique
    $original_slug = $slug;
    $counter = 1;
    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() == 0) {
            break;
        }
        $slug = $original_slug . '-' . $counter;
        $counter++;
    }
    
    // Generate excerpt if not provided
    if (empty($excerpt)) {
        $excerpt = truncateText(strip_tags($content), 150);
    }
    
    // Validate status
    if (!in_array($status, ['draft', 'published'])) {
        $status = 'published';
    }
    
    // Validate and get category ID
    $category_id = null;
    if (!empty($category_slug)) {
        $stmt = $pdo->prepare("SELECT id FROM categories WHERE slug = ?");
        $stmt->execute([$category_slug]);
        $category = $stmt->fetch();
        if ($category) {
            $category_id = $category['id'];
        } else {
            // Create category if it doesn't exist
            $category_name = ucwords(str_replace('-', ' ', $category_slug));
            $stmt = $pdo->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
            $stmt->execute([$category_name, $category_slug, "Auto-created category for: $category_name"]);
            $category_id = $pdo->lastInsertId();
        }
    }
    
    // Verify author exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->execute([$author_id]);
    if (!$stmt->fetch()) {
        $author_id = 1; // Fallback to admin user
    }
    
    // Insert the post
    $stmt = $pdo->prepare("
        INSERT INTO posts (
            title, slug, content, excerpt, featured_image, 
            category_id, author_id, status, views, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
    ");
    
    $stmt->execute([
        $title, $slug, $content, $excerpt, $featured_image,
        $category_id, $author_id, $status
    ]);
    
    $post_id = $pdo->lastInsertId();
    
    // Handle tags
    $tag_ids = [];
    if (!empty($tags) && is_array($tags)) {
        foreach ($tags as $tag_name) {
            $tag_name = trim($tag_name);
            if (empty($tag_name)) continue;
            
            $tag_slug = createSlug($tag_name);
            
            // Check if tag exists
            $stmt = $pdo->prepare("SELECT id FROM tags WHERE slug = ?");
            $stmt->execute([$tag_slug]);
            $tag = $stmt->fetch();
            
            if ($tag) {
                $tag_id = $tag['id'];
            } else {
                // Create new tag
                $stmt = $pdo->prepare("INSERT INTO tags (name, slug) VALUES (?, ?)");
                $stmt->execute([$tag_name, $tag_slug]);
                $tag_id = $pdo->lastInsertId();
            }
            
            $tag_ids[] = $tag_id;
            
            // Link tag to post
            $stmt = $pdo->prepare("INSERT IGNORE INTO post_tags (post_id, tag_id) VALUES (?, ?)");
            $stmt->execute([$post_id, $tag_id]);
        }
    }
    
    // Get the created post data
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name, c.slug as category_slug, u.username as author_name 
        FROM posts p 
        LEFT JOIN categories c ON p.category_id = c.id 
        LEFT JOIN users u ON p.author_id = u.id 
        WHERE p.id = ?
    ");
    $stmt->execute([$post_id]);
    $created_post = $stmt->fetch();
    
    // Get post tags
    $stmt = $pdo->prepare("
        SELECT t.name, t.slug 
        FROM tags t 
        JOIN post_tags pt ON t.id = pt.tag_id 
        WHERE pt.post_id = ?
    ");
    $stmt->execute([$post_id]);
    $post_tags = $stmt->fetchAll();
    
    // Prepare response data
    $response_data = [
        'post_id' => $post_id,
        'title' => $created_post['title'],
        'slug' => $created_post['slug'],
        'status' => $created_post['status'],
        'category' => [
            'id' => $created_post['category_id'],
            'name' => $created_post['category_name'],
            'slug' => $created_post['category_slug']
        ],
        'author' => [
            'id' => $created_post['author_id'],
            'name' => $created_post['author_name']
        ],
        'tags' => $post_tags,
        'featured_image' => $created_post['featured_image'],
        'excerpt' => $created_post['excerpt'],
        'created_at' => $created_post['created_at'],
        'post_url' => 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/post.php?slug=' . $created_post['slug']
    ];
    
    // Log successful creation
    logWebhook('Post created successfully', [
        'post_id' => $post_id,
        'title' => $title,
        'slug' => $slug,
        'status' => $status
    ]);
    
    sendResponse(true, 'Blog post created successfully', $response_data, 201);
    
} catch (PDOException $e) {
    logWebhook('Database error', ['error' => $e->getMessage()]);
    sendResponse(false, 'Database error occurred', null, 500);
} catch (Exception $e) {
    logWebhook('General error', ['error' => $e->getMessage()]);
    sendResponse(false, 'An error occurred while creating the post', null, 500);
}
?>
