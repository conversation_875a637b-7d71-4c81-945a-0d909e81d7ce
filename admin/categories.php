<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_category']) || isset($_POST['update_category'])) {
        $name = trim($_POST['name']);
        $slug = trim($_POST['slug']);
        $description = trim($_POST['description']);
        
        // Generate slug if empty
        if (empty($slug)) {
            $slug = createSlug($name);
        }
        
        if (isset($_POST['add_category'])) {
            // Add new category
            try {
                $stmt = $pdo->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
                $stmt->execute([$name, $slug, $description]);
                $message = "Category created successfully!";
                $action = 'list';
            } catch (PDOException $e) {
                $error = "Error creating category: " . $e->getMessage();
            }
        } else {
            // Update existing category
            $category_id = $_POST['category_id'];
            try {
                $stmt = $pdo->prepare("UPDATE categories SET name = ?, slug = ?, description = ? WHERE id = ?");
                $stmt->execute([$name, $slug, $description, $category_id]);
                $message = "Category updated successfully!";
                $action = 'list';
            } catch (PDOException $e) {
                $error = "Error updating category: " . $e->getMessage();
            }
        }
    }
}

// Handle delete action
if ($action === 'delete' && isset($_GET['id'])) {
    try {
        // Check if category has posts
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE category_id = ?");
        $stmt->execute([$_GET['id']]);
        $post_count = $stmt->fetchColumn();
        
        if ($post_count > 0) {
            $error = "Cannot delete category. It has $post_count posts assigned to it.";
        } else {
            $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
            $stmt->execute([$_GET['id']]);
            $message = "Category deleted successfully!";
        }
        $action = 'list';
    } catch (PDOException $e) {
        $error = "Error deleting category: " . $e->getMessage();
    }
}

// Get category data for editing
$category = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $category = $stmt->fetch();
    if (!$category) {
        $error = "Category not found!";
        $action = 'list';
    }
}

// Get all categories for listing
if ($action === 'list') {
    $stmt = $pdo->query("SELECT c.*, COUNT(p.id) as post_count 
                        FROM categories c 
                        LEFT JOIN posts p ON c.id = p.category_id 
                        GROUP BY c.id 
                        ORDER BY c.name");
    $categories = $stmt->fetchAll();
}

$page_title = 'Manage Categories';
include 'includes/admin_header.php';
?>

<div class="admin-content">
    <div class="page-header">
        <h1><i class="fas fa-folder"></i> Manage Categories</h1>
        <?php if ($action === 'list'): ?>
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Category
            </a>
        <?php endif; ?>
    </div>
    
    <?php if ($message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($action === 'list'): ?>
        <!-- Categories List -->
        <div class="admin-table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Slug</th>
                        <th>Description</th>
                        <th>Posts</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $cat): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($cat['name']); ?></strong>
                            </td>
                            <td>
                                <code><?php echo htmlspecialchars($cat['slug']); ?></code>
                            </td>
                            <td>
                                <?php echo $cat['description'] ? htmlspecialchars(substr($cat['description'], 0, 100)) . '...' : 'No description'; ?>
                            </td>
                            <td>
                                <span class="post-count"><?php echo $cat['post_count']; ?> posts</span>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($cat['created_at'])); ?></td>
                            <td class="actions">
                                <a href="?action=edit&id=<?php echo $cat['id']; ?>" class="btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="../category.php?slug=<?php echo $cat['slug']; ?>" target="_blank" class="btn-sm btn-secondary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <?php if ($cat['post_count'] == 0): ?>
                                    <a href="?action=delete&id=<?php echo $cat['id']; ?>" 
                                       onclick="return confirm('Are you sure you want to delete this category?')" 
                                       class="btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                <?php else: ?>
                                    <span class="btn-sm btn-disabled" title="Cannot delete category with posts">
                                        <i class="fas fa-trash"></i> Delete
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Category Form -->
        <div class="admin-form-container">
            <form method="POST" class="admin-form">
                <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                <?php endif; ?>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Category Name *</label>
                        <input type="text" id="name" name="name" required 
                               value="<?php echo $category ? htmlspecialchars($category['name']) : ''; ?>"
                               placeholder="e.g., Technology">
                    </div>
                    
                    <div class="form-group">
                        <label for="slug">Slug</label>
                        <input type="text" id="slug" name="slug" 
                               value="<?php echo $category ? htmlspecialchars($category['slug']) : ''; ?>"
                               placeholder="Auto-generated from name">
                        <small>Used in URLs. Leave empty to auto-generate.</small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" rows="4" 
                              placeholder="Brief description of this category"><?php echo $category ? htmlspecialchars($category['description']) : ''; ?></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" name="<?php echo $action === 'edit' ? 'update_category' : 'add_category'; ?>" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?php echo $action === 'edit' ? 'Update Category' : 'Create Category'; ?>
                    </button>
                    <a href="?action=list" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    <?php endif; ?>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}
.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}
.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
.admin-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}
.admin-table {
    width: 100%;
    border-collapse: collapse;
}
.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}
.admin-table th {
    background: var(--light);
    font-weight: 600;
}
.actions {
    white-space: nowrap;
}
.btn-danger {
    background: var(--danger);
    color: white;
}
.btn-danger:hover {
    background: #c82333;
}
.btn-disabled {
    background: var(--gray);
    color: white;
    cursor: not-allowed;
    opacity: 0.6;
}
.admin-form-container {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 2rem;
}
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}
.form-group {
    margin-bottom: 1.5rem;
}
.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}
.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 1rem;
}
.form-group small {
    color: var(--gray);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
}
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}
code {
    background: var(--light);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9rem;
}
.post-count {
    background: var(--primary);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
}
</style>

<?php include 'includes/admin_footer.php'; ?>
