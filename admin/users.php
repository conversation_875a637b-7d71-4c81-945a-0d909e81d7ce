<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit();
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_user']) || isset($_POST['update_user'])) {
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);
        $role = $_POST['role'];
        $password = $_POST['password'];
        
        if (isset($_POST['add_user'])) {
            // Add new user
            if (empty($password)) {
                $error = "Password is required for new users.";
            } else {
                try {
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$username, $email, $hashed_password, $role]);
                    $message = "User created successfully!";
                    $action = 'list';
                } catch (PDOException $e) {
                    $error = "Error creating user: " . $e->getMessage();
                }
            }
        } else {
            // Update existing user
            $user_id = $_POST['user_id'];
            try {
                if (!empty($password)) {
                    // Update with new password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ?, role = ? WHERE id = ?");
                    $stmt->execute([$username, $email, $hashed_password, $role, $user_id]);
                } else {
                    // Update without changing password
                    $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, role = ? WHERE id = ?");
                    $stmt->execute([$username, $email, $role, $user_id]);
                }
                $message = "User updated successfully!";
                $action = 'list';
            } catch (PDOException $e) {
                $error = "Error updating user: " . $e->getMessage();
            }
        }
    }
}

// Handle delete action
if ($action === 'delete' && isset($_GET['id'])) {
    $user_id = $_GET['id'];
    
    // Prevent deleting own account
    if ($user_id == $_SESSION['user_id']) {
        $error = "You cannot delete your own account.";
    } else {
        try {
            // Check if user has posts
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE author_id = ?");
            $stmt->execute([$user_id]);
            $post_count = $stmt->fetchColumn();
            
            if ($post_count > 0) {
                $error = "Cannot delete user. They have $post_count posts. Please reassign or delete their posts first.";
            } else {
                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                $message = "User deleted successfully!";
            }
        } catch (PDOException $e) {
            $error = "Error deleting user: " . $e->getMessage();
        }
    }
    $action = 'list';
}

// Get user data for editing
$user = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $user = $stmt->fetch();
    if (!$user) {
        $error = "User not found!";
        $action = 'list';
    }
}

// Get all users for listing
if ($action === 'list') {
    $stmt = $pdo->query("SELECT u.*, COUNT(p.id) as post_count 
                        FROM users u 
                        LEFT JOIN posts p ON u.id = p.author_id 
                        GROUP BY u.id 
                        ORDER BY u.created_at DESC");
    $users = $stmt->fetchAll();
}

$page_title = 'Manage Users';
include 'includes/admin_header.php';
?>

<div class="admin-content">
    <div class="page-header">
        <h1><i class="fas fa-users"></i> Manage Users</h1>
        <?php if ($action === 'list'): ?>
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New User
            </a>
        <?php endif; ?>
    </div>
    
    <?php if ($message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($action === 'list'): ?>
        <!-- Users List -->
        <div class="admin-table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Posts</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user_item): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($user_item['username']); ?></strong>
                                <?php if ($user_item['id'] == $_SESSION['user_id']): ?>
                                    <span class="current-user">(You)</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($user_item['email']); ?></td>
                            <td>
                                <span class="role role-<?php echo $user_item['role']; ?>">
                                    <?php echo ucfirst($user_item['role']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="post-count"><?php echo $user_item['post_count']; ?> posts</span>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($user_item['created_at'])); ?></td>
                            <td class="actions">
                                <a href="?action=edit&id=<?php echo $user_item['id']; ?>" class="btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <?php if ($user_item['id'] != $_SESSION['user_id']): ?>
                                    <?php if ($user_item['post_count'] == 0): ?>
                                        <a href="?action=delete&id=<?php echo $user_item['id']; ?>" 
                                           onclick="return confirm('Are you sure you want to delete this user?')" 
                                           class="btn-sm btn-danger">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    <?php else: ?>
                                        <span class="btn-sm btn-disabled" title="Cannot delete user with posts">
                                            <i class="fas fa-trash"></i> Delete
                                        </span>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit User Form -->
        <div class="admin-form-container">
            <form method="POST" class="admin-form">
                <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                <?php endif; ?>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">Username *</label>
                        <input type="text" id="username" name="username" required 
                               value="<?php echo $user ? htmlspecialchars($user['username']) : ''; ?>"
                               placeholder="Enter username">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required 
                               value="<?php echo $user ? htmlspecialchars($user['email']) : ''; ?>"
                               placeholder="Enter email address">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="role">Role *</label>
                        <select id="role" name="role" required>
                            <option value="editor" <?php echo ($user && $user['role'] === 'editor') ? 'selected' : ''; ?>>Editor</option>
                            <option value="admin" <?php echo ($user && $user['role'] === 'admin') ? 'selected' : ''; ?>>Admin</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password <?php echo $action === 'add' ? '*' : '(leave empty to keep current)'; ?></label>
                        <input type="password" id="password" name="password" 
                               <?php echo $action === 'add' ? 'required' : ''; ?>
                               placeholder="Enter password">
                    </div>
                </div>
                
                <?php if ($action === 'edit'): ?>
                    <div class="form-info">
                        <p><strong>Note:</strong> Leave password field empty if you don't want to change the current password.</p>
                    </div>
                <?php endif; ?>
                
                <div class="form-actions">
                    <button type="submit" name="<?php echo $action === 'edit' ? 'update_user' : 'add_user'; ?>" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?php echo $action === 'edit' ? 'Update User' : 'Create User'; ?>
                    </button>
                    <a href="?action=list" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    <?php endif; ?>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}
.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}
.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
.admin-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}
.admin-table {
    width: 100%;
    border-collapse: collapse;
}
.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}
.admin-table th {
    background: var(--light);
    font-weight: 600;
}
.current-user {
    color: var(--primary);
    font-size: 0.8rem;
    font-weight: normal;
}
.role {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}
.role-admin {
    background: #e8f4ff;
    color: var(--primary);
}
.role-editor {
    background: #fff3cd;
    color: #856404;
}
.post-count {
    background: var(--gray);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
}
.actions {
    white-space: nowrap;
}
.btn-danger {
    background: var(--danger);
    color: white;
}
.btn-danger:hover {
    background: #c82333;
}
.btn-disabled {
    background: var(--gray);
    color: white;
    cursor: not-allowed;
    opacity: 0.6;
}
.admin-form-container {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 2rem;
}
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}
.form-group {
    margin-bottom: 1.5rem;
}
.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}
.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 1rem;
}
.form-info {
    background: var(--light);
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}
</style>

<?php include 'includes/admin_footer.php'; ?>
