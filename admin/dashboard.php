<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get dashboard statistics
$stmt = $pdo->query("SELECT COUNT(*) FROM posts");
$total_posts = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM posts WHERE status = 'published'");
$published_posts = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM posts WHERE status = 'draft'");
$draft_posts = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM categories");
$total_categories = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM comments WHERE status = 'pending'");
$pending_comments = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT SUM(views) FROM posts");
$total_views = $stmt->fetchColumn() ?: 0;

// Get recent posts
$stmt = $pdo->prepare("SELECT p.*, c.name as category_name, u.username as author_name 
                      FROM posts p 
                      LEFT JOIN categories c ON p.category_id = c.id 
                      LEFT JOIN users u ON p.author_id = u.id 
                      ORDER BY p.created_at DESC 
                      LIMIT 5");
$stmt->execute();
$recent_posts = $stmt->fetchAll();

// Get recent comments
$stmt = $pdo->prepare("SELECT c.*, p.title as post_title 
                      FROM comments c 
                      JOIN posts p ON c.post_id = p.id 
                      ORDER BY c.created_at DESC 
                      LIMIT 5");
$stmt->execute();
$recent_comments = $stmt->fetchAll();

include 'includes/admin_header.php';
?>

<div class="admin-content">
    <div class="page-header">
        <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
        <p>Welcome back, <?php echo htmlspecialchars($_SESSION['username']); ?>!</p>
    </div>
    
    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $total_posts; ?></h3>
                <p>Total Posts</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon published">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $published_posts; ?></h3>
                <p>Published Posts</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon draft">
                <i class="fas fa-edit"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $draft_posts; ?></h3>
                <p>Draft Posts</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-folder"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $total_categories; ?></h3>
                <p>Categories</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon pending">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $pending_comments; ?></h3>
                <p>Pending Comments</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon views">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo number_format($total_views); ?></h3>
                <p>Total Views</p>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions">
        <h2><i class="fas fa-bolt"></i> Quick Actions</h2>
        <div class="action-buttons">
            <a href="posts.php?action=add" class="action-btn primary">
                <i class="fas fa-plus"></i> New Post
            </a>
            <a href="categories.php?action=add" class="action-btn secondary">
                <i class="fas fa-folder-plus"></i> New Category
            </a>
            <a href="comments.php" class="action-btn warning">
                <i class="fas fa-comments"></i> Manage Comments
            </a>
            <a href="../index.php" target="_blank" class="action-btn info">
                <i class="fas fa-external-link-alt"></i> View Blog
            </a>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="dashboard-grid">
        <!-- Recent Posts -->
        <div class="dashboard-widget">
            <div class="widget-header">
                <h3><i class="fas fa-file-alt"></i> Recent Posts</h3>
                <a href="posts.php" class="widget-link">View All</a>
            </div>
            <div class="widget-content">
                <?php if (empty($recent_posts)): ?>
                    <p class="no-data">No posts found.</p>
                <?php else: ?>
                    <?php foreach ($recent_posts as $post): ?>
                        <div class="recent-item">
                            <div class="item-content">
                                <h4><a href="posts.php?action=edit&id=<?php echo $post['id']; ?>"><?php echo htmlspecialchars($post['title']); ?></a></h4>
                                <div class="item-meta">
                                    <span class="status status-<?php echo $post['status']; ?>"><?php echo ucfirst($post['status']); ?></span>
                                    <span><?php echo date('M j, Y', strtotime($post['created_at'])); ?></span>
                                    <span><?php echo $post['views']; ?> views</span>
                                </div>
                            </div>
                            <div class="item-actions">
                                <a href="posts.php?action=edit&id=<?php echo $post['id']; ?>" class="btn-sm btn-primary">Edit</a>
                                <a href="../post.php?slug=<?php echo $post['slug']; ?>" target="_blank" class="btn-sm btn-secondary">View</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Comments -->
        <div class="dashboard-widget">
            <div class="widget-header">
                <h3><i class="fas fa-comments"></i> Recent Comments</h3>
                <a href="comments.php" class="widget-link">View All</a>
            </div>
            <div class="widget-content">
                <?php if (empty($recent_comments)): ?>
                    <p class="no-data">No comments found.</p>
                <?php else: ?>
                    <?php foreach ($recent_comments as $comment): ?>
                        <div class="recent-item">
                            <div class="item-content">
                                <h4><?php echo htmlspecialchars($comment['name']); ?></h4>
                                <p><?php echo htmlspecialchars(substr($comment['comment'], 0, 100)) . (strlen($comment['comment']) > 100 ? '...' : ''); ?></p>
                                <div class="item-meta">
                                    <span class="status status-<?php echo $comment['status']; ?>"><?php echo ucfirst($comment['status']); ?></span>
                                    <span>on "<?php echo htmlspecialchars($comment['post_title']); ?>"</span>
                                    <span><?php echo date('M j, Y', strtotime($comment['created_at'])); ?></span>
                                </div>
                            </div>
                            <div class="item-actions">
                                <a href="comments.php?action=edit&id=<?php echo $comment['id']; ?>" class="btn-sm btn-primary">Manage</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin_footer.php'; ?>
