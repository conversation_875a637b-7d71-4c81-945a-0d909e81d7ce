<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_post']) || isset($_POST['update_post'])) {
        $title = trim($_POST['title']);
        $slug = trim($_POST['slug']);
        $content = trim($_POST['content']);
        $excerpt = trim($_POST['excerpt']);
        $category_id = $_POST['category_id'] ?: null;
        $status = $_POST['status'];
        $featured_image = trim($_POST['featured_image']);
        
        // Generate slug if empty
        if (empty($slug)) {
            $slug = createSlug($title);
        }
        
        if (isset($_POST['add_post'])) {
            // Add new post
            try {
                $stmt = $pdo->prepare("INSERT INTO posts (title, slug, content, excerpt, category_id, author_id, status, featured_image) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$title, $slug, $content, $excerpt, $category_id, $_SESSION['user_id'], $status, $featured_image]);
                $message = "Post created successfully!";
                $action = 'list';
            } catch (PDOException $e) {
                $error = "Error creating post: " . $e->getMessage();
            }
        } else {
            // Update existing post
            $post_id = $_POST['post_id'];
            try {
                $stmt = $pdo->prepare("UPDATE posts SET title = ?, slug = ?, content = ?, excerpt = ?, category_id = ?, status = ?, featured_image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$title, $slug, $content, $excerpt, $category_id, $status, $featured_image, $post_id]);
                $message = "Post updated successfully!";
                $action = 'list';
            } catch (PDOException $e) {
                $error = "Error updating post: " . $e->getMessage();
            }
        }
    }
}

// Handle delete action
if ($action === 'delete' && isset($_GET['id'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM posts WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        $message = "Post deleted successfully!";
        $action = 'list';
    } catch (PDOException $e) {
        $error = "Error deleting post: " . $e->getMessage();
    }
}

// Get categories for dropdown
$categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();

// Get post data for editing
$post = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $pdo->prepare("SELECT * FROM posts WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $post = $stmt->fetch();
    if (!$post) {
        $error = "Post not found!";
        $action = 'list';
    }
}

// Get all posts for listing
if ($action === 'list') {
    $page = $_GET['page'] ?? 1;
    $limit = 10;
    $offset = ($page - 1) * $limit;
    
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name, u.username as author_name 
                          FROM posts p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          LEFT JOIN users u ON p.author_id = u.id 
                          ORDER BY p.created_at DESC 
                          LIMIT " . (int)$limit . " OFFSET " . (int)$offset);
    $stmt->execute();
    $posts = $stmt->fetchAll();
    
    // Get total count for pagination
    $total_posts = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
    $total_pages = ceil($total_posts / $limit);
}

$page_title = 'Manage Posts';
include 'includes/admin_header.php';
?>

<div class="admin-content">
    <div class="page-header">
        <h1><i class="fas fa-file-alt"></i> Manage Posts</h1>
        <?php if ($action === 'list'): ?>
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Post
            </a>
        <?php endif; ?>
    </div>
    
    <?php if ($message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($action === 'list'): ?>
        <!-- Posts List -->
        <div class="admin-table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Category</th>
                        <th>Author</th>
                        <th>Status</th>
                        <th>Views</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($posts as $post_item): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($post_item['title']); ?></strong>
                                <div class="post-slug"><?php echo htmlspecialchars($post_item['slug']); ?></div>
                            </td>
                            <td><?php echo $post_item['category_name'] ? htmlspecialchars($post_item['category_name']) : 'Uncategorized'; ?></td>
                            <td><?php echo htmlspecialchars($post_item['author_name']); ?></td>
                            <td>
                                <span class="status status-<?php echo $post_item['status']; ?>">
                                    <?php echo ucfirst($post_item['status']); ?>
                                </span>
                            </td>
                            <td><?php echo $post_item['views']; ?></td>
                            <td><?php echo date('M j, Y', strtotime($post_item['created_at'])); ?></td>
                            <td class="actions">
                                <a href="?action=edit&id=<?php echo $post_item['id']; ?>" class="btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="../post.php?slug=<?php echo $post_item['slug']; ?>" target="_blank" class="btn-sm btn-secondary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="?action=delete&id=<?php echo $post_item['id']; ?>" 
                                   onclick="return confirm('Are you sure you want to delete this post?')" 
                                   class="btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <a href="?page=<?php echo $i; ?>" <?php echo $i == $page ? 'class="current"' : ''; ?>>
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                </div>
            <?php endif; ?>
        </div>
        
    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Post Form -->
        <div class="admin-form-container">
            <form method="POST" class="admin-form">
                <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                <?php endif; ?>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="title">Title *</label>
                        <input type="text" id="title" name="title" required 
                               value="<?php echo $post ? htmlspecialchars($post['title']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="slug">Slug</label>
                        <input type="text" id="slug" name="slug" 
                               value="<?php echo $post ? htmlspecialchars($post['slug']) : ''; ?>"
                               placeholder="Auto-generated from title">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="excerpt">Excerpt</label>
                    <textarea id="excerpt" name="excerpt" rows="3" 
                              placeholder="Brief description of the post"><?php echo $post ? htmlspecialchars($post['excerpt']) : ''; ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="content">Content *</label>
                    <textarea id="content" name="content" rows="15" required 
                              placeholder="Write your post content here..."><?php echo $post ? htmlspecialchars($post['content']) : ''; ?></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="category_id">Category</label>
                        <select id="category_id" name="category_id">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" 
                                        <?php echo ($post && $post['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" required>
                            <option value="draft" <?php echo ($post && $post['status'] === 'draft') ? 'selected' : ''; ?>>Draft</option>
                            <option value="published" <?php echo ($post && $post['status'] === 'published') ? 'selected' : ''; ?>>Published</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="featured_image">Featured Image URL</label>
                    <input type="url" id="featured_image" name="featured_image" 
                           value="<?php echo $post ? htmlspecialchars($post['featured_image']) : ''; ?>"
                           placeholder="https://example.com/image.jpg">
                </div>
                
                <div class="form-actions">
                    <button type="submit" name="<?php echo $action === 'edit' ? 'update_post' : 'add_post'; ?>" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?php echo $action === 'edit' ? 'Update Post' : 'Create Post'; ?>
                    </button>
                    <a href="?action=list" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    <?php endif; ?>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}
.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}
.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
.admin-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}
.admin-table {
    width: 100%;
    border-collapse: collapse;
}
.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}
.admin-table th {
    background: var(--light);
    font-weight: 600;
}
.post-slug {
    font-size: 0.8rem;
    color: var(--gray);
}
.actions {
    white-space: nowrap;
}
.btn-danger {
    background: var(--danger);
    color: white;
}
.btn-danger:hover {
    background: #c82333;
}
.admin-form-container {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 2rem;
}
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}
.form-group {
    margin-bottom: 1.5rem;
}
.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}
.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 1rem;
}
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}
</style>

<?php include 'includes/admin_footer.php'; ?>
