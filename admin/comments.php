<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle comment actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_status'])) {
        $comment_id = $_POST['comment_id'];
        $status = $_POST['status'];
        
        try {
            $stmt = $pdo->prepare("UPDATE comments SET status = ? WHERE id = ?");
            $stmt->execute([$status, $comment_id]);
            $message = "Comment status updated successfully!";
        } catch (PDOException $e) {
            $error = "Error updating comment: " . $e->getMessage();
        }
    }
    
    if (isset($_POST['bulk_action']) && isset($_POST['comment_ids'])) {
        $bulk_action = $_POST['bulk_action'];
        $comment_ids = $_POST['comment_ids'];
        
        if ($bulk_action === 'approve') {
            $stmt = $pdo->prepare("UPDATE comments SET status = 'approved' WHERE id = ?");
            foreach ($comment_ids as $id) {
                $stmt->execute([$id]);
            }
            $message = count($comment_ids) . " comments approved successfully!";
        } elseif ($bulk_action === 'reject') {
            $stmt = $pdo->prepare("UPDATE comments SET status = 'rejected' WHERE id = ?");
            foreach ($comment_ids as $id) {
                $stmt->execute([$id]);
            }
            $message = count($comment_ids) . " comments rejected successfully!";
        } elseif ($bulk_action === 'delete') {
            $stmt = $pdo->prepare("DELETE FROM comments WHERE id = ?");
            foreach ($comment_ids as $id) {
                $stmt->execute([$id]);
            }
            $message = count($comment_ids) . " comments deleted successfully!";
        }
    }
}

// Handle delete action
if ($action === 'delete' && isset($_GET['id'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM comments WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        $message = "Comment deleted successfully!";
        $action = 'list';
    } catch (PDOException $e) {
        $error = "Error deleting comment: " . $e->getMessage();
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$page = $_GET['page'] ?? 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query based on filters
$where_clause = "";
$params = [];

if ($status_filter !== 'all') {
    $where_clause = "WHERE c.status = ?";
    $params[] = $status_filter;
}

// Get comments with pagination
$stmt = $pdo->prepare("SELECT c.*, p.title as post_title, p.slug as post_slug 
                      FROM comments c 
                      JOIN posts p ON c.post_id = p.id 
                      $where_clause 
                      ORDER BY c.created_at DESC 
                      LIMIT " . (int)$limit . " OFFSET " . (int)$offset);
$stmt->execute($params);
$comments = $stmt->fetchAll();

// Get total count for pagination
$count_stmt = $pdo->prepare("SELECT COUNT(*) FROM comments c JOIN posts p ON c.post_id = p.id $where_clause");
$count_stmt->execute($params);
$total_comments = $count_stmt->fetchColumn();
$total_pages = ceil($total_comments / $limit);

// Get comment counts by status
$pending_count = $pdo->query("SELECT COUNT(*) FROM comments WHERE status = 'pending'")->fetchColumn();
$approved_count = $pdo->query("SELECT COUNT(*) FROM comments WHERE status = 'approved'")->fetchColumn();
$rejected_count = $pdo->query("SELECT COUNT(*) FROM comments WHERE status = 'rejected'")->fetchColumn();
$total_count = $pdo->query("SELECT COUNT(*) FROM comments")->fetchColumn();

$page_title = 'Manage Comments';
include 'includes/admin_header.php';
?>

<div class="admin-content">
    <div class="page-header">
        <h1><i class="fas fa-comments"></i> Manage Comments</h1>
        <div class="comment-stats">
            <span class="stat-item">
                <i class="fas fa-clock"></i> <?php echo $pending_count; ?> Pending
            </span>
            <span class="stat-item">
                <i class="fas fa-check"></i> <?php echo $approved_count; ?> Approved
            </span>
            <span class="stat-item">
                <i class="fas fa-times"></i> <?php echo $rejected_count; ?> Rejected
            </span>
        </div>
    </div>
    
    <?php if ($message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <a href="?status=all" class="tab <?php echo $status_filter === 'all' ? 'active' : ''; ?>">
            All (<?php echo $total_count; ?>)
        </a>
        <a href="?status=pending" class="tab <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
            Pending (<?php echo $pending_count; ?>)
        </a>
        <a href="?status=approved" class="tab <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
            Approved (<?php echo $approved_count; ?>)
        </a>
        <a href="?status=rejected" class="tab <?php echo $status_filter === 'rejected' ? 'active' : ''; ?>">
            Rejected (<?php echo $rejected_count; ?>)
        </a>
    </div>
    
    <!-- Comments List -->
    <form method="POST" class="bulk-actions-form">
        <div class="bulk-actions">
            <select name="bulk_action" class="bulk-action-select">
                <option value="">Bulk Actions</option>
                <option value="approve">Approve</option>
                <option value="reject">Reject</option>
                <option value="delete">Delete</option>
            </select>
            <button type="submit" class="btn btn-secondary apply-bulk-action" disabled>Apply</button>
        </div>
        
        <div class="admin-table-container">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th width="30">
                            <input type="checkbox" id="select-all">
                        </th>
                        <th>Comment</th>
                        <th>Post</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($comments)): ?>
                        <tr>
                            <td colspan="6" class="no-data">No comments found.</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($comments as $comment): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="comment_ids[]" value="<?php echo $comment['id']; ?>" class="item-checkbox">
                                </td>
                                <td>
                                    <div class="comment-info">
                                        <strong><?php echo htmlspecialchars($comment['name']); ?></strong>
                                        <span class="email"><?php echo htmlspecialchars($comment['email']); ?></span>
                                    </div>
                                    <div class="comment-text">
                                        <?php echo htmlspecialchars(substr($comment['comment'], 0, 150)) . (strlen($comment['comment']) > 150 ? '...' : ''); ?>
                                    </div>
                                </td>
                                <td>
                                    <a href="../post.php?slug=<?php echo $comment['post_slug']; ?>" target="_blank">
                                        <?php echo htmlspecialchars($comment['post_title']); ?>
                                    </a>
                                </td>
                                <td>
                                    <span class="status status-<?php echo $comment['status']; ?>">
                                        <?php echo ucfirst($comment['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y g:i A', strtotime($comment['created_at'])); ?></td>
                                <td class="actions">
                                    <?php if ($comment['status'] === 'pending'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                            <input type="hidden" name="status" value="approved">
                                            <button type="submit" name="update_status" class="btn-sm btn-success">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        </form>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" name="update_status" class="btn-sm btn-warning">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        </form>
                                    <?php elseif ($comment['status'] === 'approved'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" name="update_status" class="btn-sm btn-warning">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        </form>
                                    <?php elseif ($comment['status'] === 'rejected'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                            <input type="hidden" name="status" value="approved">
                                            <button type="submit" name="update_status" class="btn-sm btn-success">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <a href="?action=delete&id=<?php echo $comment['id']; ?>" 
                                       onclick="return confirm('Are you sure you want to delete this comment?')" 
                                       class="btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </form>
    
    <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <a href="?status=<?php echo $status_filter; ?>&page=<?php echo $i; ?>" 
                   <?php echo $i == $page ? 'class="current"' : ''; ?>>
                    <?php echo $i; ?>
                </a>
            <?php endfor; ?>
        </div>
    <?php endif; ?>
</div>

<style>
.comment-stats {
    display: flex;
    gap: 1rem;
}
.stat-item {
    background: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    box-shadow: var(--shadow);
    font-size: 0.9rem;
}
.filter-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}
.tab {
    padding: 0.8rem 1.5rem;
    background: var(--white);
    border-radius: 4px;
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}
.tab:hover,
.tab.active {
    background: var(--primary);
    color: var(--white);
}
.bulk-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: center;
}
.comment-info {
    margin-bottom: 0.5rem;
}
.comment-info .email {
    color: var(--gray);
    font-size: 0.9rem;
    margin-left: 0.5rem;
}
.comment-text {
    color: var(--dark);
    line-height: 1.4;
}
.btn-success {
    background: var(--success);
    color: white;
}
.btn-success:hover {
    background: #218838;
}
.btn-warning {
    background: var(--warning);
    color: var(--dark);
}
.btn-warning:hover {
    background: #e0a800;
}
</style>

<?php include 'includes/admin_footer.php'; ?>
