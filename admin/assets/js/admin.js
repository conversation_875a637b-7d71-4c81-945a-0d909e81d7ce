// Admin Panel JavaScript

// Sidebar toggle functionality
const sidebarToggle = document.getElementById('sidebar-toggle');
const sidebar = document.querySelector('.admin-sidebar');

if (sidebarToggle && sidebar) {
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('active');
    });
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        }
    });
}

// Confirm delete actions
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('delete-btn') || e.target.closest('.delete-btn')) {
        if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            e.preventDefault();
        }
    }
});

// Auto-hide alerts after 5 seconds
const alerts = document.querySelectorAll('.alert');
alerts.forEach(alert => {
    setTimeout(() => {
        alert.style.opacity = '0';
        setTimeout(() => {
            alert.remove();
        }, 300);
    }, 5000);
});

// Form validation
const forms = document.querySelectorAll('form');
forms.forEach(form => {
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('error');
                isValid = false;
            } else {
                field.classList.remove('error');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});

// Character counter for textareas
const textareas = document.querySelectorAll('textarea[data-max-length]');
textareas.forEach(textarea => {
    const maxLength = parseInt(textarea.dataset.maxLength);
    const counter = document.createElement('div');
    counter.className = 'char-counter';
    counter.style.cssText = 'text-align: right; font-size: 0.8rem; color: var(--gray); margin-top: 0.25rem;';
    
    textarea.parentNode.appendChild(counter);
    
    function updateCounter() {
        const remaining = maxLength - textarea.value.length;
        counter.textContent = `${remaining} characters remaining`;
        
        if (remaining < 0) {
            counter.style.color = 'var(--danger)';
        } else if (remaining < 50) {
            counter.style.color = 'var(--warning)';
        } else {
            counter.style.color = 'var(--gray)';
        }
    }
    
    textarea.addEventListener('input', updateCounter);
    updateCounter();
});

// Slug generator
const titleInput = document.querySelector('input[name="title"]');
const slugInput = document.querySelector('input[name="slug"]');

if (titleInput && slugInput) {
    titleInput.addEventListener('input', function() {
        if (!slugInput.dataset.manual) {
            const slug = this.value
                .toLowerCase()
                .trim()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '');
            
            slugInput.value = slug;
        }
    });
    
    slugInput.addEventListener('input', function() {
        this.dataset.manual = 'true';
    });
}

// Image preview
const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
imageInputs.forEach(input => {
    input.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                let preview = input.parentNode.querySelector('.image-preview');
                if (!preview) {
                    preview = document.createElement('div');
                    preview.className = 'image-preview';
                    preview.style.cssText = 'margin-top: 1rem; text-align: center;';
                    input.parentNode.appendChild(preview);
                }
                
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 4px; box-shadow: var(--shadow);">
                    <p style="margin-top: 0.5rem; font-size: 0.8rem; color: var(--gray);">${file.name}</p>
                `;
            };
            reader.readAsDataURL(file);
        }
    });
});

// Sortable tables
const sortableTables = document.querySelectorAll('.sortable-table');
sortableTables.forEach(table => {
    const headers = table.querySelectorAll('th[data-sort]');
    
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.innerHTML += ' <i class="fas fa-sort" style="opacity: 0.5; margin-left: 0.5rem;"></i>';
        
        header.addEventListener('click', function() {
            const column = this.dataset.sort;
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            const isAscending = this.classList.contains('sort-asc');
            
            // Remove sort classes from all headers
            headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
            
            // Add appropriate class to current header
            this.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
            
            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.querySelector(`[data-sort="${column}"]`).textContent.trim();
                const bValue = b.querySelector(`[data-sort="${column}"]`).textContent.trim();
                
                if (isAscending) {
                    return bValue.localeCompare(aValue, undefined, { numeric: true });
                } else {
                    return aValue.localeCompare(bValue, undefined, { numeric: true });
                }
            });
            
            // Reorder rows in DOM
            rows.forEach(row => tbody.appendChild(row));
        });
    });
});

// Bulk actions
const bulkActionForm = document.querySelector('.bulk-actions-form');
if (bulkActionForm) {
    const selectAllCheckbox = bulkActionForm.querySelector('#select-all');
    const itemCheckboxes = bulkActionForm.querySelectorAll('.item-checkbox');
    const bulkActionSelect = bulkActionForm.querySelector('.bulk-action-select');
    const applyButton = bulkActionForm.querySelector('.apply-bulk-action');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionButton();
        });
    }
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButton);
    });
    
    function updateBulkActionButton() {
        const checkedItems = bulkActionForm.querySelectorAll('.item-checkbox:checked');
        if (applyButton) {
            applyButton.disabled = checkedItems.length === 0;
        }
    }
    
    if (applyButton) {
        applyButton.addEventListener('click', function(e) {
            const checkedItems = bulkActionForm.querySelectorAll('.item-checkbox:checked');
            const action = bulkActionSelect ? bulkActionSelect.value : '';
            
            if (checkedItems.length === 0) {
                e.preventDefault();
                alert('Please select at least one item.');
                return;
            }
            
            if (!action) {
                e.preventDefault();
                alert('Please select an action.');
                return;
            }
            
            if (action === 'delete') {
                if (!confirm(`Are you sure you want to delete ${checkedItems.length} item(s)? This action cannot be undone.`)) {
                    e.preventDefault();
                }
            }
        });
    }
}

// Auto-save functionality for forms
const autoSaveForms = document.querySelectorAll('.auto-save-form');
autoSaveForms.forEach(form => {
    let saveTimeout;
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                autoSave(form);
            }, 2000);
        });
    });
    
    function autoSave(form) {
        const formData = new FormData(form);
        formData.append('auto_save', '1');
        
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Draft saved automatically', 'success');
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
        });
    }
});

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 4px;
        color: white;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    switch (type) {
        case 'success':
            notification.style.background = 'var(--success)';
            break;
        case 'error':
            notification.style.background = 'var(--danger)';
            break;
        case 'warning':
            notification.style.background = 'var(--warning)';
            notification.style.color = 'var(--dark)';
            break;
        default:
            notification.style.background = 'var(--info)';
    }
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .error {
        border-color: var(--danger) !important;
        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
`;
document.head.appendChild(style);

// Initialize tooltips (if needed)
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel initialized');
});
