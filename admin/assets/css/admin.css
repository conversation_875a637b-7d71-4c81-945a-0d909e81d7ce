:root {
    --primary: #2c5cc5;
    --primary-dark: #1a3d8f;
    --secondary: #4caf50;
    --accent: #ff9800;
    --dark: #2c3e50;
    --light: #f8f9fa;
    --gray: #6c757d;
    --white: #ffffff;
    --light-gray: #e9ecef;
    --border: #dee2e6;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
    --shadow: 0 4px 12px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
    --sidebar-width: 260px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--light);
    color: var(--dark);
    line-height: 1.6;
}

/* Admin Layout */
.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--white);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--transition);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--primary);
    font-weight: 700;
    font-size: 1.3rem;
}

.sidebar-header .logo i {
    margin-right: 0.5rem;
    color: var(--accent);
}

.sidebar-nav ul {
    list-style: none;
    padding: 1rem 0;
}

.sidebar-nav li {
    margin: 0.25rem 0;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.8rem 1.5rem;
    color: var(--dark);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    background: rgba(44, 92, 197, 0.1);
    border-left-color: var(--primary);
    color: var(--primary);
}

.sidebar-nav a i {
    width: 20px;
    margin-right: 0.8rem;
    text-align: center;
}

.nav-divider {
    height: 1px;
    background: var(--border);
    margin: 1rem 0;
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
}

/* Header */
.admin-header {
    background: var(--white);
    padding: 1rem 2rem;
    box-shadow: var(--shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--gray);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--light-gray);
    color: var(--primary);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    text-align: right;
}

.user-name {
    display: block;
    font-weight: 600;
    color: var(--dark);
}

.user-role {
    font-size: 0.85rem;
    color: var(--gray);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Content Area */
.admin-content {
    padding: 2rem;
    flex: 1;
}

.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.page-header p {
    color: var(--gray);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary);
    color: var(--white);
    font-size: 1.5rem;
}

.stat-icon.published {
    background: var(--success);
}

.stat-icon.draft {
    background: var(--warning);
}

.stat-icon.pending {
    background: var(--danger);
}

.stat-icon.views {
    background: var(--info);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--gray);
    font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.quick-actions h2 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn.primary {
    background: var(--primary);
    color: var(--white);
}

.action-btn.primary:hover {
    background: var(--primary-dark);
}

.action-btn.secondary {
    background: var(--secondary);
    color: var(--white);
}

.action-btn.secondary:hover {
    background: #45a049;
}

.action-btn.warning {
    background: var(--warning);
    color: var(--dark);
}

.action-btn.warning:hover {
    background: #e0a800;
}

.action-btn.info {
    background: var(--info);
    color: var(--white);
}

.action-btn.info:hover {
    background: #138496;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-widget {
    background: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.widget-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-header h3 {
    color: var(--primary-dark);
}

.widget-link {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.widget-link:hover {
    color: var(--primary-dark);
}

.widget-content {
    padding: 1rem;
}

.recent-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid var(--light-gray);
}

.recent-item:last-child {
    border-bottom: none;
}

.item-content {
    flex: 1;
}

.item-content h4 {
    margin-bottom: 0.5rem;
}

.item-content h4 a {
    color: var(--dark);
    text-decoration: none;
    transition: var(--transition);
}

.item-content h4 a:hover {
    color: var(--primary);
}

.item-content p {
    color: var(--gray);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.item-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--gray);
    flex-wrap: wrap;
}

.status {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-published {
    background: #d4edda;
    color: #155724;
}

.status-draft {
    background: #fff3cd;
    color: #856404;
}

.status-pending {
    background: #f8d7da;
    color: #721c24;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--gray);
    color: var(--white);
}

.btn-secondary:hover {
    background: #5a6268;
}

.no-data {
    text-align: center;
    color: var(--gray);
    padding: 2rem;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.active {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-content {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
}
