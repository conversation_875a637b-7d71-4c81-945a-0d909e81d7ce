
:root {
    --primary: #2c5cc5;
    --primary-dark: #1a3d8f;
    --secondary: #4caf50;
    --accent: #ff9800;
    --dark: #2c3e50;
    --light: #f8f9fa;
    --gray: #6c757d;
    --white: #ffffff;
    --shadow: 0 4px 12px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--light);
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
header {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 0;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
    z-index: 1001;
}

.logo i {
    margin-right: 0.5rem;
    color: var(--secondary);
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    transition: var(--transition);
}

.nav-links a:hover {
    color: var(--primary);
}

.cta-button {
    background-color: var(--accent);
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.cta-button:hover {
    background-color: #e68a00;
    transform: translateY(-3px);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1001;
}

.menu-toggle span {
    height: 3px;
    width: 100%;
    background-color: var(--dark);
    border-radius: 3px;
    transition: var(--transition);
}

/* Mobile Menu Styles */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
    transform: translateY(-100%);
    transition: transform 0.5s ease;
}

.mobile-nav.active {
    transform: translateY(0);
}

.mobile-nav ul {
    list-style: none;
    text-align: center;
}

.mobile-nav ul li {
    margin: 1.5rem 0;
}

.mobile-nav ul li a {
    text-decoration: none;
    color: var(--dark);
    font-size: 1.5rem;
    font-weight: 500;
    transition: var(--transition);
}

.mobile-nav ul li a:hover {
    color: var(--primary);
}

.mobile-cta {
    margin-top: 2rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0.7)), url('https://images.unsplash.com/photo-1491841573634-28140fc7ced7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
    background-size: cover;
    background-position: center;
    color: var(--dark);
    padding: 8rem 0 5rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-text {
    flex: 1;
    max-width: 600px;
    padding-right: 2rem;
}

.hero h1 {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--primary-dark);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: var(--dark);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    min-height: 400px;
    background: url('https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80') center/cover no-repeat;
    border-radius: 10px;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

/* How It Works */
.section {
    padding: 5rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-title h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--accent);
}

.section-title p {
    color: var(--gray);
    max-width: 700px;
    margin: 2rem auto 0;
}

.steps {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.step-card {
    flex: 0 0 calc(25% - 2rem);
    background: var(--white);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    margin: 1rem;
    transition: var(--transition);
    border-top: 4px solid var(--primary);
}

.step-card:hover {
    transform: translateY(-10px);
}

.step-number {
    width: 50px;
    height: 50px;
    background: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
}

.step-card h3 {
    margin-bottom: 1rem;
    color: var(--dark);
}

/* Pricing Section */
.pricing {
    background-color: var(--white);
}

.pricing-plans {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.pricing-plan {
    flex: 0 0 calc(50% - 2rem);
    background: white;
    border-radius: 8px;
    padding: 2.5rem;
    margin: 1rem;
    box-shadow: var(--shadow);
    text-align: center;
    border: 2px solid var(--light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.pricing-plan::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: var(--primary);
}

.pricing-plan:hover {
    border-color: var(--primary);
}

.pricing-plan h3 {
    font-size: 1.8rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 1.5rem 0;
    color: var(--dark);
}

.price span {
    font-size: 1rem;
    font-weight: normal;
    color: var(--gray);
}

.features {
    list-style: none;
    margin: 2rem 0;
    text-align: left;
}

.features li {
    padding: 0.7rem 0;
    border-bottom: 1px solid #eee;
}

.features li:last-child {
    border-bottom: none;
}

.features li i {
    color: var(--secondary);
    margin-right: 0.5rem;
}

.highlight {
    background-color: var(--accent);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    display: inline-block;
    margin-top: 1rem;
}

/* FAQ Section */
.faq {
    background-color: var(--light);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--white);
    margin-bottom: 1rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--primary);
}

.faq-question {
    padding: 1.5rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
}

.faq-answer {
    padding: 0 1.5rem 1.5rem;
    color: var(--gray);
    display: none;
}

.faq-question i {
    transition: var(--transition);
    color: var(--primary);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-item.active .faq-answer {
    display: block;
}

/* Testimonials */
.testimonials {
    background-color: #eef7ff;
}

.testimonial-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.testimonial-card {
    flex: 0 0 calc(33.333% - 2rem);
    background: var(--white);
    border-radius: 8px;
    padding: 2rem;
    margin: 1rem;
    box-shadow: var(--shadow);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 4rem;
    color: rgba(44, 92, 197, 0.1);
    font-family: serif;
    line-height: 1;
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.student-info {
    display: flex;
    align-items: center;
}

.student-avatar {
    width: 50px;
    height: 50px;
    background-color: var(--accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    color: white;
    margin-right: 1rem;
}

.student-details h4 {
    font-weight: 600;
    color: var(--primary);
}

.student-details p {
    font-style: normal;
    font-size: 0.9rem;
    color: var(--gray);
    margin: 0;
}

/* Contact Section */
.contact {
    background-color: var(--white);
}

.contact-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.contact-form {
    flex: 1;
    padding: 2rem;
    min-width: 300px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(44, 92, 197, 0.1);
}

.contact-image {
    flex: 1;
    text-align: center;
    min-width: 300px;
    min-height: 400px;
    background: url('https://images.unsplash.com/photo-1586769852836-bc069f19e1b6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80') center/cover no-repeat;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

/* Footer */
footer {
    background-color: var(--dark);
    color: white;
    padding: 3rem 0 1.5rem;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    min-width: 250px;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
    color: var(--accent);
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--accent);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: #ddd;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--accent);
    margin-left: 5px;
}

.social-icons {
    display: flex;
    margin-top: 1.5rem;
}

.social-icons a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 1rem;
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--accent);
    transform: translateY(-5px);
}

.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #aaa;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-text {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .step-card {
        flex: 0 0 calc(50% - 2rem);
    }
    
    .testimonial-card {
        flex: 0 0 calc(50% - 2rem);
    }
}

@media (max-width: 768px) {
    .nav-links, .cta-button {
        display: none;
    }
    
    .menu-toggle {
        display: flex;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    .step-card {
        flex: 0 0 100%;
    }
    
    .pricing-plan {
        flex: 0 0 100%;
    }
    
    .testimonial-card {
        flex: 0 0 100%;
    }
    
    .contact-container {
        flex-direction: column;
    }
    
    .hero-image {
        min-height: 300px;
        margin-top: 2rem;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    .contact-form {
        padding: 1rem;
    }
    
    .cta-button {
        width: 100%;
        padding: 0.9rem;
        font-size: 1rem;
    }
}
