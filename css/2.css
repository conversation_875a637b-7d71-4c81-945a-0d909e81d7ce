
:root {
    --primary: #2c5cc5;
    --primary-dark: #1a3d8f;
    --secondary: #4caf50;
    --accent: #ff9800;
    --dark: #2c3e50;
    --light: #f8f9fa;
    --gray: #6c757d;
    --white: #ffffff;
    --shadow: 0 4px 12px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--light);
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
header {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 0;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
    z-index: 1001;
}

.logo i {
    margin-right: 0.5rem;
    color: var(--secondary);
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    transition: var(--transition);
}

.nav-links a:hover {
    color: var(--primary);
}

.cta-button {
    background-color: var(--accent);
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.cta-button:hover {
    background-color: #e68a00;
    transform: translateY(-3px);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1001;
}

.menu-toggle span {
    height: 3px;
    width: 100%;
    background-color: var(--dark);
    border-radius: 3px;
    transition: var(--transition);
}

/* Mobile Menu Styles */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
    transform: translateY(-100%);
    transition: transform 0.5s ease;
}

.mobile-nav.active {
    transform: translateY(0);
}

.mobile-nav ul {
    list-style: none;
    text-align: center;
}

.mobile-nav ul li {
    margin: 1.5rem 0;
}

.mobile-nav ul li a {
    text-decoration: none;
    color: var(--dark);
    font-size: 1.5rem;
    font-weight: 500;
    transition: var(--transition);
}

.mobile-nav ul li a:hover {
    color: var(--primary);
}

.mobile-cta {
    margin-top: 2rem;
}

/* Hero Section for Search */
.search-hero {
    background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0.7)), url('https://images.unsplash.com/photo-1507842217343-583bb7270b66?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1153&q=80');
    background-size: cover;
    background-position: center;
    color: var(--dark);
    padding: 10rem 0 5rem;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.search-hero h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-dark);
}

.search-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto 2rem;
    color: var(--dark);
}

/* Search Section */
.search-section {
    padding: 3rem 0;
    background-color: var(--white);
}

.search-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
}

.search-filters {
    flex: 0 0 300px;
    background: var(--white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: var(--shadow);
    height: fit-content;
}

.search-results {
    flex: 1;
    min-width: 300px;
}

.filter-group {
    margin-bottom: 2rem;
}

.filter-group h3 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light);
    color: var(--primary);
}

.filter-options {
    list-style: none;
}

.filter-options li {
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
}

.filter-options input[type="checkbox"] {
    margin-right: 0.8rem;
    width: 18px;
    height: 18px;
    accent-color: var(--primary);
}

.filter-options label {
    cursor: pointer;
    transition: var(--transition);
}

.filter-options label:hover {
    color: var(--primary);
}

.search-bar {
    display: flex;
    margin-bottom: 2rem;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.search-bar input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid var(--primary);
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
    outline: none;
}

.search-bar button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 0 1.5rem;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: var(--transition);
}

.search-bar button:hover {
    background: var(--primary-dark);
}

.results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light);
}

.sort-options select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.book-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
}

.book-card {
    background: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-top: 4px solid var(--primary);
}

.book-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.book-image {
    height: 200px;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray);
}

.book-image i {
    font-size: 3rem;
    color: var(--gray);
}

.book-details {
    padding: 1.5rem;
}

.book-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary);
}

.book-author {
    color: var(--gray);
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
}

.book-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.book-condition {
    background: #e8f4ff;
    color: var(--primary);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
}

.book-price {
    font-weight: 700;
    color: var(--dark);
}

.book-rating {
    color: var(--accent);
    margin-bottom: 1rem;
}

.book-actions {
    display: flex;
    justify-content: space-between;
}

.btn-rent {
    background: var(--accent);
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-rent:hover {
    background: #e68a00;
}

.btn-details {
    background: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-details:hover {
    background: var(--primary);
    color: white;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
    gap: 0.5rem;
}

.pagination button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
}

.pagination button:hover, 
.pagination button.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* Footer */
footer {
    background-color: var(--dark);
    color: white;
    padding: 3rem 0 1.5rem;
    margin-top: 5rem;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    min-width: 250px;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
    color: var(--accent);
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--accent);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: #ddd;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--accent);
    margin-left: 5px;
}

.social-icons {
    display: flex;
    margin-top: 1.5rem;
}

.social-icons a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 1rem;
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--accent);
    transform: translateY(-5px);
}

.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #aaa;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .book-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-links, .cta-button {
        display: none;
    }
    
    .menu-toggle {
        display: flex;
    }
    
    .search-container {
        flex-direction: column;
    }
    
    .search-filters {
        flex: 0 0 auto;
        width: 100%;
    }
    
    .search-hero h1 {
        font-size: 2.2rem;
    }
    
    .search-hero p {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .search-hero h1 {
        font-size: 1.8rem;
    }
    
    .search-hero {
        padding: 8rem 0 3rem;
    }
    
    .book-grid {
        grid-template-columns: 1fr;
    }
    
    .results-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .book-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-rent, .btn-details {
        width: 100%;
        text-align: center;
    }
}
