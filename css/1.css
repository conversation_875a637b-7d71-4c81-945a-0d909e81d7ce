:root {
    --primary: #2c5cc5;
    --primary-dark: #1a3d8f;
    --secondary: #4caf50;
    --accent: #ff9800;
    --dark: #2c3e50;
    --light: #f8f9fa;
    --gray: #6c757d;
    --white: #ffffff;
    --light-gray: #e9ecef;
    --shadow: 0 4px 12px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--light);
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
header {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 0;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
    z-index: 1001;
}

.logo i {
    margin-right: 0.5rem;
    color: var(--secondary);
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    transition: var(--transition);
}

.nav-links a:hover {
    color: var(--primary);
}

.cta-button {
    background-color: var(--accent);
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.cta-button:hover {
    background-color: #e68a00;
    transform: translateY(-3px);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1001;
}

.menu-toggle span {
    height: 3px;
    width: 100%;
    background-color: var(--dark);
    border-radius: 3px;
    transition: var(--transition);
}

/* Mobile Menu Styles */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
    transform: translateY(-100%);
    transition: transform 0.5s ease;
}

.mobile-nav.active {
    transform: translateY(0);
}

.mobile-nav ul {
    list-style: none;
    text-align: center;
}

.mobile-nav ul li {
    margin: 1.5rem 0;
}

.mobile-nav ul li a {
    text-decoration: none;
    color: var(--dark);
    font-size: 1.5rem;
    font-weight: 500;
    transition: var(--transition);
}

.mobile-nav ul li a:hover {
    color: var(--primary);
}

.mobile-cta {
    margin-top: 2rem;
}

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(to right, rgba(44, 92, 197, 0.85), rgba(26, 61, 143, 0.85)), url('https://images.unsplash.com/photo-1491841550275-ad7854e35ca6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80');
    background-size: cover;
    background-position: center;
    color: var(--white);
    padding: 10rem 0 5rem;
    text-align: center;
}

.blog-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.blog-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    opacity: 0.9;
}

/* Blog Content */
.blog-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2.5rem;
    padding: 5rem 0;
}

.blog-posts {
    flex: 1;
    min-width: 300px;
}

.blog-sidebar {
    flex: 0 0 320px;
}

/* Post Card */
.post-card {
    background: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 2.5rem;
    transition: var(--transition);
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.post-image {
    height: 250px;
    background-color: #f0f0f0;
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.post-card:hover .post-image img {
    transform: scale(1.05);
}

.post-content {
    padding: 1.8rem;
}

.post-meta {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--gray);
    font-size: 0.9rem;
}

.post-meta span {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
}

.post-meta i {
    margin-right: 0.5rem;
    color: var(--primary);
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.tag {
    background: #e8f4ff;
    color: var(--primary);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.tag:hover {
    background: var(--primary);
    color: white;
    cursor: pointer;
}

.post-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-dark);
}

.post-excerpt {
    margin-bottom: 1.5rem;
    color: var(--dark);
}

.read-more {
    display: inline-flex;
    align-items: center;
    color: var(--primary);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.read-more i {
    margin-left: 0.5rem;
    transition: var(--transition);
}

.read-more:hover {
    color: var(--primary-dark);
}

.read-more:hover i {
    transform: translateX(5px);
}

/* Sidebar */
.sidebar-widget {
    background: var(--white);
    border-radius: 8px;
    padding: 1.8rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.widget-title {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid var(--light-gray);
    color: var(--primary);
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--accent);
}

/* Search Widget */
.blog-search {
    display: flex;
    margin-bottom: 1rem;
}

.blog-search input {
    flex: 1;
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--light-gray);
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
    outline: none;
}

.blog-search button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 0 1.2rem;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: var(--transition);
}

.blog-search button:hover {
    background: var(--primary-dark);
}

/* Categories Widget */
.categories-list {
    list-style: none;
}

.categories-list li {
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    justify-content: space-between;
}

.categories-list li:last-child {
    border-bottom: none;
}

.categories-list a {
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}

.categories-list a:hover {
    color: var(--primary);
}

.category-count {
    background: var(--light-gray);
    color: var(--gray);
    padding: 0.2rem 0.6rem;
    border-radius: 20px;
    font-size: 0.85rem;
}

/* Popular Posts */
.popular-post {
    display: flex;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--light-gray);
}

.popular-post:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.popular-post-img {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.popular-post-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-post-content h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.popular-post-content h4 a {
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}

.popular-post-content h4 a:hover {
    color: var(--primary);
}

.popular-post-date {
    font-size: 0.85rem;
    color: var(--gray);
}

/* Newsletter Widget */
.newsletter-form input {
    width: 100%;
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--light-gray);
    border-radius: 4px;
    font-size: 1rem;
    margin-bottom: 1rem;
    outline: none;
}

/* Tags Widget */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    gap: 0.5rem;
}

.pagination a {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}

.pagination a:hover, 
.pagination a.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* Footer */
footer {
    background-color: var(--dark);
    color: white;
    padding: 3rem 0 1.5rem;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    min-width: 250px;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
    color: var(--accent);
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--accent);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: #ddd;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--accent);
    margin-left: 5px;
}

.social-icons {
    display: flex;
    margin-top: 1.5rem;
}

.social-icons a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 1rem;
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--accent);
    transform: translateY(-5px);
}

.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #aaa;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .blog-hero h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .nav-links, .cta-button {
        display: none;
    }
    
    .menu-toggle {
        display: flex;
    }
    
    .blog-container {
        flex-direction: column;
    }
    
    .blog-sidebar {
        flex: 0 0 auto;
        width: 100%;
    }
    
    .blog-hero h1 {
        font-size: 2.2rem;
    }
    
    .blog-hero p {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .blog-hero h1 {
        font-size: 1.8rem;
    }
    
    .blog-hero {
        padding: 8rem 0 3rem;
    }
    
    .post-meta {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .post-meta span {
        margin-bottom: 0.5rem;
    }
}