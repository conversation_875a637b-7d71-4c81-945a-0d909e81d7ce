<?php
// Debug version of index.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Index Page</h1>";

// Step 1: Test basic PHP
echo "<h2>Step 1: PHP Working</h2>";
echo "✓ PHP is working<br>";

// Step 2: Test file includes
echo "<h2>Step 2: Testing File Includes</h2>";
try {
    echo "Loading functions...<br>";
    require_once 'includes/functions.php';
    echo "✓ Functions loaded successfully<br>";
} catch (Exception $e) {
    echo "✗ Error loading functions: " . $e->getMessage() . "<br>";
    exit;
}

// Step 3: Test database functions
echo "<h2>Step 3: Testing Database Functions</h2>";
try {
    echo "Testing getCategories()...<br>";
    $categories = getCategories();
    echo "✓ Categories loaded: " . count($categories) . " found<br>";
    
    echo "Testing getPosts()...<br>";
    $posts = getPosts(5, 0);
    echo "✓ Posts loaded: " . count($posts) . " found<br>";
    
} catch (Exception $e) {
    echo "✗ Database function error: " . $e->getMessage() . "<br>";
}

// Step 4: Test header include
echo "<h2>Step 4: Testing Header Include</h2>";
try {
    $page_title = 'Debug Test';
    $page_description = 'Testing page';
    
    echo "Loading header...<br>";
    // Don't actually include header yet, just test if file exists
    if (file_exists('includes/header.php')) {
        echo "✓ Header file exists<br>";
    } else {
        echo "✗ Header file missing<br>";
    }
} catch (Exception $e) {
    echo "✗ Header error: " . $e->getMessage() . "<br>";
}

echo "<h2>Results</h2>";
echo "<p>If all steps above show ✓, then the main index.php should work.</p>";
echo "<p><a href='index.php'>Try Main Index Page</a></p>";

// Show some sample data if available
if (isset($categories) && !empty($categories)) {
    echo "<h3>Sample Categories:</h3>";
    echo "<ul>";
    foreach ($categories as $cat) {
        echo "<li>" . htmlspecialchars($cat['name']) . " (" . $cat['post_count'] . " posts)</li>";
    }
    echo "</ul>";
}

if (isset($posts) && !empty($posts)) {
    echo "<h3>Sample Posts:</h3>";
    echo "<ul>";
    foreach ($posts as $post) {
        echo "<li>" . htmlspecialchars($post['title']) . " - " . $post['status'] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<h3>No Posts Found</h3>";
    echo "<p>You may need to install sample data first.</p>";
    echo "<p><a href='install_sample_data.php'>Install Sample Data</a></p>";
}
?>
