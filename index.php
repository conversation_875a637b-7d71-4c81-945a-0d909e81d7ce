<?php
require_once 'includes/functions.php';

// Page settings
$page_title = 'Home';
$page_description = 'Welcome to Creative Blog - Discover amazing stories, insights, and inspiration from our community of writers.';
$page_keywords = 'blog, creative writing, stories, articles, inspiration';

// Pagination settings
$posts_per_page = 6;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $posts_per_page;

// Get posts and total count
$posts = getPosts($posts_per_page, $offset);
$total_posts = getTotalPosts();
$total_pages = ceil($total_posts / $posts_per_page);

// Get data for sidebar
$categories = getCategories();
$popular_posts = getPopularPosts(3);
$recent_posts = getRecentPosts(3);

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>Welcome to Creative Blog</h1>
        <p>Discover amazing stories, insights, and inspiration from our community of writers. Explore diverse topics and join our creative journey.</p>
    </div>
</section>

<!-- Main Content -->
<section class="main-content container">
    <!-- Blog Posts -->
    <main class="blog-posts">
        <?php if (empty($posts)): ?>
            <div class="no-posts">
                <h2>No posts found</h2>
                <p>There are no published posts at the moment. Please check back later!</p>
            </div>
        <?php else: ?>
            <?php foreach ($posts as $post): ?>
                <article class="post-card">
                    <div class="post-image">
                        <?php if ($post['featured_image']): ?>
                            <img src="<?php echo $post['featured_image']; ?>" alt="<?php echo htmlspecialchars($post['title']); ?>">
                        <?php else: ?>
                            <img src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="<?php echo htmlspecialchars($post['title']); ?>">
                        <?php endif; ?>
                    </div>
                    <div class="post-content">
                        <div class="post-meta">
                            <span><i class="far fa-calendar"></i> <?php echo formatDate($post['created_at']); ?></span>
                            <span><i class="far fa-user"></i> <?php echo htmlspecialchars($post['author_name']); ?></span>
                            <span><i class="far fa-eye"></i> <?php echo $post['views']; ?> views</span>
                            <?php if ($post['category_name']): ?>
                                <span><i class="far fa-folder"></i> <a href="category.php?slug=<?php echo $post['category_slug']; ?>"><?php echo htmlspecialchars($post['category_name']); ?></a></span>
                            <?php endif; ?>
                        </div>
                        
                        <h2 class="post-title">
                            <a href="post.php?slug=<?php echo $post['slug']; ?>"><?php echo htmlspecialchars($post['title']); ?></a>
                        </h2>
                        
                        <p class="post-excerpt">
                            <?php echo $post['excerpt'] ? htmlspecialchars($post['excerpt']) : truncateText(strip_tags($post['content']), 150); ?>
                        </p>
                        
                        <a href="post.php?slug=<?php echo $post['slug']; ?>" class="read-more">
                            Read More <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($current_page > 1): ?>
                        <a href="?page=<?php echo $current_page - 1; ?>"><i class="fas fa-chevron-left"></i></a>
                    <?php endif; ?>
                    
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <?php if ($i == $current_page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($current_page < $total_pages): ?>
                        <a href="?page=<?php echo $current_page + 1; ?>"><i class="fas fa-chevron-right"></i></a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>
    
    <!-- Sidebar -->
    <aside class="sidebar">
        <!-- Search Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Search Blog</h3>
            <form class="search-form" action="search.php" method="GET">
                <input type="text" name="q" placeholder="Search articles..." value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>">
                <button type="submit"><i class="fas fa-search"></i></button>
            </form>
        </div>
        
        <!-- Categories Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Categories</h3>
            <ul class="categories-list">
                <?php foreach ($categories as $category): ?>
                    <li>
                        <a href="category.php?slug=<?php echo $category['slug']; ?>"><?php echo htmlspecialchars($category['name']); ?></a>
                        <span class="category-count"><?php echo $category['post_count']; ?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- Popular Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Popular Posts</h3>
            <?php foreach ($popular_posts as $popular_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($popular_post['featured_image']): ?>
                            <img src="<?php echo $popular_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($popular_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $popular_post['slug']; ?>"><?php echo htmlspecialchars($popular_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($popular_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Recent Posts Widget -->
        <div class="sidebar-widget">
            <h3 class="widget-title">Recent Posts</h3>
            <?php foreach ($recent_posts as $recent_post): ?>
                <div class="popular-post">
                    <div class="popular-post-img">
                        <?php if ($recent_post['featured_image']): ?>
                            <img src="<?php echo $recent_post['featured_image']; ?>" alt="<?php echo htmlspecialchars($recent_post['title']); ?>">
                        <?php else: ?>
                            <i class="fas fa-image"></i>
                        <?php endif; ?>
                    </div>
                    <div class="popular-post-content">
                        <h4><a href="post.php?slug=<?php echo $recent_post['slug']; ?>"><?php echo htmlspecialchars($recent_post['title']); ?></a></h4>
                        <div class="popular-post-date"><?php echo formatDate($recent_post['created_at']); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </aside>
</section>

<?php include 'includes/footer.php'; ?>
