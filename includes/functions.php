<?php
// Check if we're in the root directory or a subdirectory
$config_path = file_exists('config/database.php') ? 'config/database.php' : '../config/database.php';
require_once $config_path;

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Function to sanitize input
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Function to create slug from title
function createSlug($string) {
    $slug = strtolower(trim($string));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    return trim($slug, '-');
}

// Function to get all posts with pagination
function getPosts($limit = 10, $offset = 0, $category_id = null, $search = null) {
    global $pdo;
    
    $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug, u.username as author_name 
            FROM posts p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN users u ON p.author_id = u.id 
            WHERE p.status = 'published'";
    
    $params = [];
    
    if ($category_id) {
        $sql .= " AND p.category_id = ?";
        $params[] = $category_id;
    }
    
    if ($search) {
        $sql .= " AND (p.title LIKE ? OR p.content LIKE ? OR p.excerpt LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $sql .= " ORDER BY p.created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// Function to get total posts count
function getTotalPosts($category_id = null, $search = null) {
    global $pdo;
    
    $sql = "SELECT COUNT(*) FROM posts WHERE status = 'published'";
    $params = [];
    
    if ($category_id) {
        $sql .= " AND category_id = ?";
        $params[] = $category_id;
    }
    
    if ($search) {
        $sql .= " AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchColumn();
}

// Function to get single post by slug
function getPostBySlug($slug) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name, c.slug as category_slug, u.username as author_name 
                          FROM posts p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          LEFT JOIN users u ON p.author_id = u.id 
                          WHERE p.slug = ? AND p.status = 'published'");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Function to get all categories
function getCategories() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT c.*, COUNT(p.id) as post_count 
                        FROM categories c 
                        LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published' 
                        GROUP BY c.id 
                        ORDER BY c.name");
    return $stmt->fetchAll();
}

// Function to get popular posts
function getPopularPosts($limit = 5) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name 
                          FROM posts p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          WHERE p.status = 'published' 
                          ORDER BY p.views DESC 
                          LIMIT ?");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

// Function to get recent posts
function getRecentPosts($limit = 5) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name 
                          FROM posts p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          WHERE p.status = 'published' 
                          ORDER BY p.created_at DESC 
                          LIMIT ?");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

// Function to increment post views
function incrementViews($post_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("UPDATE posts SET views = views + 1 WHERE id = ?");
    $stmt->execute([$post_id]);
}

// Function to get post tags
function getPostTags($post_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT t.* FROM tags t 
                          JOIN post_tags pt ON t.id = pt.tag_id 
                          WHERE pt.post_id = ?");
    $stmt->execute([$post_id]);
    return $stmt->fetchAll();
}

// Function to format date
function formatDate($date, $format = 'F j, Y') {
    return date($format, strtotime($date));
}

// Function to truncate text
function truncateText($text, $length = 150) {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . '...';
}

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// Function to redirect
function redirect($url) {
    header("Location: $url");
    exit();
}

// Function to get comments for a post
function getComments($post_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM comments WHERE post_id = ? AND status = 'approved' ORDER BY created_at DESC");
    $stmt->execute([$post_id]);
    return $stmt->fetchAll();
}

// Function to add comment
function addComment($post_id, $name, $email, $comment) {
    global $pdo;
    
    $stmt = $pdo->prepare("INSERT INTO comments (post_id, name, email, comment) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$post_id, $name, $email, $comment]);
}
?>
