<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Study Blog | MedBookRent</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Existing styles remain unchanged */
        :root {
            --primary: #2c5cc5;
            --primary-dark: #1a3d8f;
            --secondary: #4caf50;
            --accent: #ff9800;
            --dark: #2c3e50;
            --light: #f8f9fa;
            --light-gray: #e9ecef;
            --medium-gray: #ced4da;
            --gray: #6c757d;
            --white: #ffffff;
            --shadow: 0 4px 12px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background-color: var(--light);
            overflow-x: hidden;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* Header Styles */
        header {
            background-color: var(--white);
            box-shadow: var(--shadow);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.2rem 0;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
            z-index: 1001;
            text-decoration: none;
        }
        
        .logo i {
            margin-right: 0.5rem;
            color: var(--secondary);
        }
        
        .nav-links {
            display: flex;
            list-style: none;
        }
        
        .nav-links li {
            margin-left: 2rem;
        }
        
        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: var(--transition);
        }
        
        .nav-links a:hover, 
        .nav-links a.active {
            color: var(--primary);
        }
        
        .cta-button {
            background-color: var(--accent);
            color: white;
            border: none;
            padding: 0.7rem 1.5rem;
            border-radius: 4px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }
        
        .cta-button:hover {
            background-color: #e68a00;
            transform: translateY(-3px);
        }
        
        .menu-toggle {
            display: none;
            flex-direction: column;
            justify-content: space-between;
            width: 30px;
            height: 21px;
            cursor: pointer;
            z-index: 1001;
        }
        
        .menu-toggle span {
            height: 3px;
            width: 100%;
            background-color: var(--dark);
            border-radius: 3px;
            transition: var(--transition);
        }
        
        /* Mobile Menu Styles */
        .mobile-nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: var(--white);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 999;
            transform: translateY(-100%);
            transition: transform 0.5s ease;
        }
        
        .mobile-nav.active {
            transform: translateY(0);
        }
        
        .mobile-nav ul {
            list-style: none;
            text-align: center;
        }
        
        .mobile-nav ul li {
            margin: 1.5rem 0;
        }
        
        .mobile-nav ul li a {
            text-decoration: none;
            color: var(--dark);
            font-size: 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .mobile-nav ul li a:hover,
        .mobile-nav ul li a.active {
            color: var(--primary);
        }
        
        .mobile-cta {
            margin-top: 2rem;
        }
        
        /* Main Content Area */
        .main-content {
            margin-top: 80px;
        }
        
        /* Blog Hero Section */
        .blog-hero {
            background: linear-gradient(to right, rgba(44, 92, 197, 0.85), rgba(26, 61, 143, 0.85)), url('https://images.unsplash.com/photo-1491841550275-ad7854e35ca6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80');
            background-size: cover;
            background-position: center;
            color: var(--white);
            padding: 8rem 0 5rem;
            text-align: center;
        }
        
        .blog-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .blog-hero p {
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
            opacity: 0.9;
        }
        
        /* Breadcrumb */
        .breadcrumb {
            background-color: var(--light-gray);
            padding: 1rem 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .breadcrumb span {
            color: var(--gray);
            margin: 0 0.5rem;
        }
        
        /* Blog Content */
        .blog-container {
            display: flex;
            flex-wrap: wrap;
            gap: 2.5rem;
            padding: 5rem 0;
        }
        
        .blog-posts {
            flex: 1;
            min-width: 300px;
        }
        
        .blog-sidebar {
            flex: 0 0 320px;
        }
        
        /* Post Card */
        .post-card {
            background: var(--white);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-bottom: 2.5rem;
            transition: var(--transition);
        }
        
        .post-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .post-image {
            height: 250px;
            background-color: #f0f0f0;
            overflow: hidden;
        }
        
        .post-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }
        
        .post-card:hover .post-image img {
            transform: scale(1.05);
        }
        
        .post-content {
            padding: 1.8rem;
        }
        
        .post-meta {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        .post-meta span {
            display: flex;
            align-items: center;
            margin-right: 1.5rem;
        }
        
        .post-meta i {
            margin-right: 0.5rem;
            color: var(--primary);
        }
        
        .post-tags {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }
        
        .tag {
            background: #e8f4ff;
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: var(--transition);
            text-decoration: none;
        }
        
        .tag:hover {
            background: var(--primary);
            color: white;
            cursor: pointer;
        }
        
        .post-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--primary-dark);
        }
        
        .post-title a {
            color: inherit;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .post-title a:hover {
            color: var(--accent);
        }
        
        .post-excerpt {
            margin-bottom: 1.5rem;
            color: var(--dark);
        }
        
        .read-more {
            display: inline-flex;
            align-items: center;
            color: var(--primary);
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .read-more i {
            margin-left: 0.5rem;
            transition: var(--transition);
        }
        
        .read-more:hover {
            color: var(--primary-dark);
        }
        
        .read-more:hover i {
            transform: translateX(5px);
        }
        
        /* Single Post */
        .single-post {
            background: var(--white);
            border-radius: 8px;
            box-shadow: var(--shadow);
            padding: 3rem;
            margin-bottom: 3rem;
        }
        
        .single-post-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .single-post-title {
            font-size: 2.2rem;
            margin-bottom: 1rem;
            color: var(--primary-dark);
        }
        
        .post-content p {
            margin-bottom: 1.5rem;
        }
        
        .post-content h2 {
            color: var(--primary);
            margin: 2rem 0 1rem;
        }
        
        .post-content h3 {
            color: var(--primary-dark);
            margin: 1.5rem 0 1rem;
        }
        
        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1.5rem 0;
        }
        
        .post-content blockquote {
            border-left: 4px solid var(--accent);
            padding: 1rem 1.5rem;
            background: var(--light);
            margin: 1.5rem 0;
            font-style: italic;
        }
        
        .author-box {
            display: flex;
            align-items: center;
            background: var(--light);
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        
        .author-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 1.5rem;
            flex-shrink: 0;
        }
        
        .author-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .author-info h4 {
            margin-bottom: 0.5rem;
            color: var(--primary-dark);
        }
        
        .author-social {
            margin-top: 0.5rem;
        }
        
        .author-social a {
            display: inline-block;
            margin-right: 0.8rem;
            color: var(--gray);
            transition: var(--transition);
        }
        
        .author-social a:hover {
            color: var(--primary);
        }
        
        .related-posts {
            margin: 3rem 0;
        }
        
        .section-title {
            font-size: 1.8rem;
            color: var(--primary-dark);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--light-gray);
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background-color: var(--accent);
        }
        
        /* Sidebar */
        .sidebar-widget {
            background: var(--white);
            border-radius: 8px;
            padding: 1.8rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        
        .widget-title {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.8rem;
            border-bottom: 2px solid var(--light-gray);
            color: var(--primary);
            position: relative;
        }
        
        .widget-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background-color: var(--accent);
        }
        
        /* Search Widget */
        .blog-search {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .blog-search input {
            flex: 1;
            padding: 0.8rem 1.2rem;
            border: 2px solid var(--light-gray);
            border-radius: 4px 0 0 4px;
            font-size: 1rem;
            outline: none;
        }
        
        .blog-search button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0 1.2rem;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .blog-search button:hover {
            background: var(--primary-dark);
        }
        
        /* Categories Widget */
        .categories-list {
            list-style: none;
        }
        
        .categories-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
        }
        
        .categories-list li:last-child {
            border-bottom: none;
        }
        
        .categories-list a {
            text-decoration: none;
            color: var(--dark);
            transition: var(--transition);
        }
        
        .categories-list a:hover {
            color: var(--primary);
        }
        
        .category-count {
            background: var(--light-gray);
            color: var(--gray);
            padding: 0.2rem 0.6rem;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        
        /* Popular Posts */
        .popular-post {
            display: flex;
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .popular-post:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .popular-post-img {
            width: 80px;
            height: 80px;
            border-radius: 4px;
            overflow: hidden;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .popular-post-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .popular-post-content h4 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .popular-post-content h4 a {
            text-decoration: none;
            color: var(--dark);
            transition: var(--transition);
        }
        
        .popular-post-content h4 a:hover {
            color: var(--primary);
        }
        
        .popular-post-date {
            font-size: 0.85rem;
            color: var(--gray);
        }
        
        /* Newsletter Widget */
        .newsletter-form input {
            width: 100%;
            padding: 0.8rem 1.2rem;
            border: 2px solid var(--light-gray);
            border-radius: 4px;
            font-size: 1rem;
            margin-bottom: 1rem;
            outline: none;
        }
        
        /* Tags Widget */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
            gap: 0.5rem;
        }
        
        .pagination a {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            text-decoration: none;
            color: var(--dark);
            transition: var(--transition);
        }
        
        .pagination a:hover, 
        .pagination a.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        /* Footer */
        footer {
            background-color: var(--dark);
            color: white;
            padding: 3rem 0 1.5rem;
        }
        
        .footer-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        
        .footer-section {
            flex: 1;
            min-width: 250px;
            margin-bottom: 2rem;
        }
        
        .footer-section h3 {
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.5rem;
            color: var(--accent);
        }
        
        .footer-section h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--accent);
        }
        
        .footer-links {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 0.8rem;
        }
        
        .footer-links a {
            color: #ddd;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer-links a:hover {
            color: var(--accent);
            margin-left: 5px;
        }
        
        .social-icons {
            display: flex;
            margin-top: 1.5rem;
        }
        
        .social-icons a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin-right: 1rem;
            transition: var(--transition);
        }
        
        .social-icons a:hover {
            background-color: var(--accent);
            transform: translateY(-5px);
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #aaa;
            font-size: 0.9rem;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .blog-hero h1 {
                font-size: 2.5rem;
            }
            
            .single-post {
                padding: 2rem;
            }
        }
        
        @media (max-width: 768px) {
            .nav-links, .cta-button {
                display: none;
            }
            
            .menu-toggle {
                display: flex;
            }
            
            .blog-container {
                flex-direction: column;
            }
            
            .blog-sidebar {
                flex: 0 0 auto;
                width: 100%;
            }
            
            .blog-hero h1 {
                font-size: 2.2rem;
            }
            
            .blog-hero p {
                font-size: 1.1rem;
            }
            
            .author-box {
                flex-direction: column;
                text-align: center;
            }
            
            .author-avatar {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }
        
        @media (max-width: 480px) {
            .blog-hero h1 {
                font-size: 1.8rem;
            }
            
            .blog-hero {
                padding: 6rem 0 3rem;
            }
            
            .post-meta {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .post-meta span {
                margin-bottom: 0.5rem;
            }
            
            .single-post {
                padding: 1.5rem;
            }
            
            .single-post-title {
                font-size: 1.8rem;
            }
        }
        /* New styles for comments section */
        .comments-section {
            margin: 3rem 0;
        }
        
        .comment-form-container {
            background: var(--white);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2.5rem;
        }
        
        .comment-form h3 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary-dark);
            position: relative;
            padding-bottom: 0.5rem;
        }
        
        .comment-form h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--accent);
        }
        
        .form-row {
            margin-bottom: 1.5rem;
        }
        
        .form-row label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-row input,
        .form-row textarea {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
        }
        
        .form-row textarea {
            min-height: 150px;
            resize: vertical;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 4px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .comments-list {
            background: var(--white);
            border-radius: 8px;
            box-shadow: var(--shadow);
            padding: 2rem;
        }
        
        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .comments-title {
            font-size: 1.5rem;
            color: var(--primary-dark);
        }
        
        .comments-count {
            color: var(--gray);
        }
        
        .comment {
            padding: 1.5rem 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .comment:last-child {
            border-bottom: none;
        }
        
        .comment-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .comment-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--light);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: var(--primary);
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .comment-author {
            font-weight: 600;
            color: var(--primary-dark);
        }
        
        .comment-meta {
            color: var(--gray);
            font-size: 0.9rem;
            display: flex;
            gap: 1rem;
        }
        
        .comment-content {
            margin-bottom: 1rem;
            line-height: 1.7;
        }
        
        .comment-actions {
            display: flex;
            gap: 1rem;
        }
        
        .like-btn {
            background: none;
            border: none;
            color: var(--gray);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
        }
        
        .like-btn:hover {
            background: rgba(44, 92, 197, 0.1);
            color: var(--primary);
        }
        
        .like-btn.liked {
            color: #dc3545;
        }
        
        .like-btn.liked:hover {
            background: rgba(220, 53, 69, 0.1);
        }
        
        .reply-btn {
            background: none;
            border: none;
            color: var(--gray);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
        }
        
        .reply-btn:hover {
            background: rgba(44, 92, 197, 0.1);
            color: var(--primary);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .comment-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .comment-avatar {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <nav class="navbar">
                <a href="#" class="logo">
                    <i class="fas fa-book-medical"></i>
                    <span>MedBookRent</span>
                </a>
                
                <!-- Desktop Navigation -->
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="#">How It Works</a></li>
                    <li><a href="#">Pricing</a></li>
                    <li><a href="#">Search Books</a></li>
                    <li><a href="blog.html" class="active">Blog</a></li>
                    <li><a href="#">FAQ</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>
                <button class="cta-button">Start Renting</button>
                
                <!-- Mobile Hamburger Menu -->
                <div class="menu-toggle" id="mobile-menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </nav>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="mobile-nav" id="mobile-nav">
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="#">How It Works</a></li>
                <li><a href="#">Pricing</a></li>
                <li><a href="#">Search Books</a></li>
                <li><a href="blog.html" class="active">Blog</a></li>
                <li><a href="#">FAQ</a></li>
                <li><a href="#">Contact</a></li>
            </ul>
            <div class="mobile-cta">
                <button class="cta-button">Start Renting</button>
            </div>
        </div>
    </header>

    <div class="main-content">
        <!-- Blog Hero Section -->
        <section class="blog-hero">
            <div class="container">
                <h1>Medical Study Insights & Resources</h1>
                <p>Expert tips, textbook reviews, and study strategies for medical students</p>
            </div>
        </section>
        
        <!-- Breadcrumb -->
        <div class="breadcrumb container">
            <a href="#">Home</a>
            <span>/</span>
            <a href="#">Blog</a>
            <span>/</span>
            <a href="#">Textbook Reviews</a>
            <span>/</span>
            <span>Comparative Review: Trueman's vs Pradeep's Biology</span>
        </div>

        <!-- Blog Content -->
        <section class="blog-container container">
            <!-- Main Content -->
            <main class="blog-posts">
                <!-- Single Post -->
                <article class="single-post">
                    <div class="single-post-header">
                        <div class="post-meta">
                            <span><i class="far fa-calendar"></i> June 12, 2025</span>
                            <span><i class="far fa-user"></i> Dr. Ananya Sharma</span>
                            <span><i class="far fa-comment"></i> 24 Comments</span>
                        </div>
                        <div class="post-tags">
                            <a href="#" class="tag">Textbook Reviews</a>
                            <a href="#" class="tag">Biology</a>
                            <a href="#" class="tag">NEET</a>
                            <a href="#" class="tag">Resources</a>
                        </div>
                        <h1 class="single-post-title">Comparative Review: Trueman's vs Pradeep's Biology Textbooks</h1>
                    </div>
                    
                    <div class="post-image">
                        <img src="https://images.unsplash.com/photo-1491841550275-ad7854e35ca6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80" alt="Biology Textbooks">
                    </div>
                    
                    <div class="post-content">
                        <!-- Post content remains unchanged -->
                        <p>Choosing the right biology textbook is crucial for medical aspirants preparing for competitive exams like NEET. Two of the most popular options are Trueman's Elementary Biology and Pradeep's A Textbook of Biology. Both have been trusted by students for decades, but which one is better for your preparation?</p>
                        
                        <h2>Content Depth and Coverage</h2>
                        <p>Trueman's Elementary Biology is known for its comprehensive coverage of the NCERT syllabus. The book delves deep into concepts with detailed explanations that help students build a strong foundation. The diagrams are meticulously drawn and labeled, making it easier to understand complex biological processes.</p>
                        
                        <p>Pradeep's Biology, on the other hand, offers a more concise approach. It covers the syllabus effectively but with less elaboration. This makes it a better choice for quick revisions and for students who already have a basic understanding of the concepts.</p>
                        
                        <img src="https://images.unsplash.com/photo-*************-1b06ac7ceec7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1332&q=80" alt="Textbook Comparison">
                        
                        <h2>Question Bank and Practice Material</h2>
                        <p>Both textbooks come with extensive question banks, but they differ in their approach:</p>
                        
                        <h3>Trueman's Biology</h3>
                        <ul>
                            <li>Includes chapter-wise exercises with both solved and unsolved questions</li>
                            <li>Previous years' NEET questions categorized by chapter</li>
                            <li>Higher-order thinking questions to challenge students</li>
                        </ul>
                        
                        <h3>Pradeep's Biology</h3>
                        <ul>
                            <li>Focuses on objective-type questions similar to NEET pattern</li>
                            <li>Includes NCERT exemplar problems with solutions</li>
                            <li>Special sections for diagram-based questions</li>
                        </ul>
                        
                        <blockquote>
                            "For conceptual clarity and building a strong foundation, Trueman's is unparalleled. But if you're looking for exam-oriented practice, Pradeep's might serve you better in the final stages of preparation." - Dr. Rajiv Mehta, NEET Mentor
                        </blockquote>
                        
                        <h2>Visual Aids and Illustrations</h2>
                        <p>When it comes to visual learning, both books have their strengths:</p>
                        <p>Trueman's has more detailed and anatomically accurate diagrams, especially in chapters like Human Physiology and Plant Anatomy. Pradeep's uses a more schematic approach with color-coded illustrations that help in quick memorization.</p>
                        
                        <h2>Recommendations</h2>
                        <p>Based on our analysis and feedback from top NEET scorers:</p>
                        <ul>
                            <li><strong>For Class 11 students:</strong> Start with Trueman's for conceptual clarity</li>
                            <li><strong>For Class 12 revision:</strong> Use Pradeep's for quick review and practice</li>
                            <li><strong>For visual learners:</strong> Trueman's detailed diagrams are superior</li>
                            <li><strong>For exam practice:</strong> Pradeep's question bank is more aligned with NEET pattern</li>
                        </ul>
                        
                        <p>Many successful students use both books - Trueman's for learning concepts and Pradeep's for practice. Renting both textbooks from MedBookRent can be a cost-effective solution for comprehensive preparation.</p>
                    </div>
                    </div>
                    
                    <!-- Author Box -->
                    <div class="author-box">
                        <!-- Author box content remains unchanged -->
                        <div class="author-avatar">
                            <img src="https://images.unsplash.com/photo-*************-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1376&q=80" alt="Dr. Ananya Sharma">
                        </div>
                        <div class="author-info">
                            <h4>Dr. Ananya Sharma</h4>
                            <p>Senior Biology faculty with 12 years of experience mentoring NEET aspirants. Former HOD at Delhi Public School and author of "Mastering Biology Concepts".</p>
                            <div class="author-social">
                                <a href="#"><i class="fab fa-twitter"></i></a>
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#"><i class="fab fa-instagram"></i></a>
                                <a href="#"><i class="fab fa-facebook-f"></i></a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Comments Section -->
                    <div class="comments-section">
                        <!-- Comment Form -->
                        <div class="comment-form-container">
                            <form class="comment-form">
                                <h3>Leave a Comment</h3>
                                <div class="form-row">
                                    <label for="comment-name">Name *</label>
                                    <input type="text" id="comment-name" required>
                                </div>
                                <div class="form-row">
                                    <label for="comment-email">Email *</label>
                                    <input type="email" id="comment-email" required>
                                </div>
                                <div class="form-row">
                                    <label for="comment-text">Your Comment *</label>
                                    <textarea id="comment-text" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Post Comment</button>
                            </form>
                        </div>
                        
                        <!-- Comments List -->
                        <div class="comments-list">
                            <div class="comments-header">
                                <h3 class="comments-title">Comments</h3>
                                <div class="comments-count">24 Comments</div>
                            </div>
                            
                            <!-- Comment 1 -->
                            <div class="comment">
                                <div class="comment-header">
                                    <div class="comment-avatar">RK</div>
                                    <div>
                                        <div class="comment-author">Rajesh Kumar</div>
                                        <div class="comment-meta">
                                            <span>June 14, 2025</span>
                                            <span>Medical Student</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="comment-content">
                                    This review is spot on! I used both books for my NEET preparation and found Trueman's better for conceptual understanding while Pradeep's had better practice questions. Great comparison!
                                </div>
                                <div class="comment-actions">
                                    <button class="like-btn" onclick="toggleLike(this)">
                                        <i class="far fa-thumbs-up"></i>
                                        <span class="like-count">42</span>
                                    </button>
                                    <button class="reply-btn">
                                        <i class="fas fa-reply"></i> Reply
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Comment 2 -->
                            <div class="comment">
                                <div class="comment-header">
                                    <div class="comment-avatar">PS</div>
                                    <div>
                                        <div class="comment-author">Priya Sharma</div>
                                        <div class="comment-meta">
                                            <span>June 13, 2025</span>
                                            <span>Biology Teacher</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="comment-content">
                                    As a biology teacher with 10 years of experience, I completely agree with your assessment. I recommend Trueman's to all my students for building strong fundamentals. The diagrams are unparalleled.
                                </div>
                                <div class="comment-actions">
                                    <button class="like-btn" onclick="toggleLike(this)">
                                        <i class="far fa-thumbs-up"></i>
                                        <span class="like-count">28</span>
                                    </button>
                                    <button class="reply-btn">
                                        <i class="fas fa-reply"></i> Reply
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Comment 3 -->
                            <div class="comment">
                                <div class="comment-header">
                                    <div class="comment-avatar">AM</div>
                                    <div>
                                        <div class="comment-author">Amit Mehta</div>
                                        <div class="comment-meta">
                                            <span>June 12, 2025</span>
                                            <span>NEET Aspirant</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="comment-content">
                                    Thank you for this detailed comparison! I was confused about which book to buy, but now I'll rent both from MedBookRent. The cost-saving tip is really helpful for students like me.
                                </div>
                                <div class="comment-actions">
                                    <button class="like-btn" onclick="toggleLike(this)">
                                        <i class="far fa-thumbs-up"></i>
                                        <span class="like-count">15</span>
                                    </button>
                                    <button class="reply-btn">
                                        <i class="fas fa-reply"></i> Reply
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Comment 4 -->
                            <div class="comment">
                                <div class="comment-header">
                                    <div class="comment-avatar">SD</div>
                                    <div>
                                        <div class="comment-author">Sanjay Desai</div>
                                        <div class="comment-meta">
                                            <span>June 12, 2025</span>
                                            <span>Parent</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="comment-content">
                                    This article helped me understand which books to get for my daughter who's preparing for NEET. The rental option from MedBookRent is a lifesaver - textbooks are so expensive these days!
                                </div>
                                <div class="comment-actions">
                                    <button class="like-btn liked" onclick="toggleLike(this)">
                                        <i class="fas fa-thumbs-up"></i>
                                        <span class="like-count">8</span>
                                    </button>
                                    <button class="reply-btn">
                                        <i class="fas fa-reply"></i> Reply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Related Posts -->
                    <div class="related-posts">
                        <!-- Related posts content remains unchanged -->
                        <h3 class="section-title">Related Articles</h3>
                        <div class="book-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 2rem;">
                            <!-- Post 1 -->
                            <div class="post-card">
                                <div class="post-image">
                                    <img src="https://images.unsplash.com/photo-1589652717521-10c0d092dea9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Anatomy Study">
                                </div>
                                <div class="post-content">
                                    <div class="post-meta">
                                        <span><i class="far fa-calendar"></i> May 10, 2025</span>
                                    </div>
                                    <h3 class="post-title"><a href="#">How to Master Human Anatomy in 90 Days</a></h3>
                                    <a href="#" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                            
                            <!-- Post 2 -->
                            <div class="post-card">
                                <div class="post-image">
                                    <img src="https://images.unsplash.com/photo-1586773860418-d37222d8fce3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1173&q=80" alt="Time Management">
                                </div>
                                <div class="post-content">
                                    <div class="post-meta">
                                        <span><i class="far fa-calendar"></i> May 28, 2025</span>
                                    </div>
                                    <h3 class="post-title"><a href="#">Creating the Perfect Study Schedule for NEET</a></h3>
                                    <a href="#" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                            
                            <!-- Post 3 -->
                            <div class="post-card">
                                <div class="post-image">
                                    <img src="https://images.unsplash.com/photo-1589998059171-988d887df646?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1176&q=80" alt="Digital Resources">
                                </div>
                                <div class="post-content">
                                    <div class="post-meta">
                                        <span><i class="far fa-calendar"></i> May 15, 2025</span>
                                    </div>
                                    <h3 class="post-title"><a href="#">Essential Digital Resources for Medical Students</a></h3>
                                    <a href="#" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </article>
            </main>
            
            <!-- Sidebar -->
            <aside class="blog-sidebar">
                <!-- Sidebar content remains unchanged -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Search Blog</h3>
                    <div class="blog-search">
                        <input type="text" placeholder="Search articles...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                </div>
                
                <!-- Categories Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Categories</h3>
                    <ul class="categories-list">
                        <li>
                            <a href="#">Study Tips & Techniques</a>
                            <span class="category-count">24</span>
                        </li>
                        <li>
                            <a href="#">Textbook Reviews</a>
                            <span class="category-count">18</span>
                        </li>
                        <li>
                            <a href="#">Exam Preparation</a>
                            <span class="category-count">15</span>
                        </li>
                        <li>
                            <a href="#">Medical Resources</a>
                            <span class="category-count">12</span>
                        </li>
                        <li>
                            <a href="#">Student Wellness</a>
                            <span class="category-count">8</span>
                        </li>
                        <li>
                            <a href="#">Career Guidance</a>
                            <span class="category-count">6</span>
                        </li>
                    </ul>
                </div>
                
                <!-- Popular Posts Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Popular Posts</h3>
                    <div class="popular-post">
                        <div class="popular-post-img">
                            <img src="https://images.unsplash.com/photo-1589652717521-10c0d092dea9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Anatomy Study">
                        </div>
                        <div class="popular-post-content">
                            <h4><a href="#">How to Master Human Anatomy in 90 Days</a></h4>
                            <div class="popular-post-date">May 10, 2025</div>
                        </div>
                    </div>
                    <div class="popular-post">
                        <div class="popular-post-img">
                            <img src="https://images.unsplash.com/photo-*************-1b06ac7ceec7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1332&q=80" alt="Chemistry Books">
                        </div>
                        <div class="popular-post-content">
                            <h4><a href="#">Best Chemistry Reference Books for Medical Aspirants</a></h4>
                            <div class="popular-post-date">April 28, 2025</div>
                        </div>
                    </div>
                    <div class="popular-post">
                        <div class="popular-post-img">
                            <img src="https://images.unsplash.com/photo-**********-cbf427effbad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Study Space">
                        </div>
                        <div class="popular-post-content">
                            <h4><a href="#">Creating the Perfect Study Environment at Home</a></h4>
                            <div class="popular-post-date">April 15, 2025</div>
                        </div>
                    </div>
                </div>
                
                <!-- Newsletter Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Subscribe to Newsletter</h3>
                    <p>Get the latest study tips and resources delivered to your inbox.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address">
                        <button type="submit" class="cta-button" style="width:100%">Subscribe</button>
                    </form>
                </div>
                
                <!-- Tags Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Popular Tags</h3>
                    <div class="tags-container">
                        <a href="#" class="tag">NEET Preparation</a>
                        <a href="#" class="tag">MCQs</a>
                        <a href="#" class="tag">Revision Techniques</a>
                        <a href="#" class="tag">Anatomy</a>
                        <a href="#" class="tag">Physiology</a>
                        <a href="#" class="tag">Biochemistry</a>
                        <a href="#" class="tag">Study Plan</a>
                        <a href="#" class="tag">Memory Techniques</a>
                        <a href="#" class="tag">Textbook Guide</a>
                        <a href="#" class="tag">Online Learning</a>
                    </div>
                </div>
            </aside>
        </section>
    </div>

    <!-- Footer -->
    <footer>
        <!-- Footer content remains unchanged -->
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>MedBookRent</h3>
                    <p>Making quality medical education accessible and affordable for every student in India through our textbook rental service.</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Blog Categories</h3>
                    <ul class="footer-links">
                        <li><a href="#">Study Tips & Techniques</a></li>
                        <li><a href="#">Textbook Reviews</a></li>
                        <li><a href="#">Exam Preparation</a></li>
                        <li><a href="#">Medical Resources</a></li>
                        <li><a href="#">Student Wellness</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="index.html">Home</a></li>
                        <li><a href="#how-it-works">How It Works</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="search.html">Search Books</a></li>
                        <li><a href="blog.html">Blog</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul class="footer-links">
                        <li><i class="fas fa-map-marker-alt"></i> 123 Education Street, Mumbai</li>
                        <li><i class="fas fa-phone"></i> +91 98765 43210</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-clock"></i> Mon-Sat: 9AM - 6PM</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 MedBookRent. All rights reserved. Designed for medical students in India.</p>
            </div>
        </div>
    </footer>

    <script>
        // Existing JavaScript remains unchanged
        // Mobile Menu Toggle
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileNav = document.getElementById('mobile-nav');
        
        mobileMenu.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
            mobileNav.classList.toggle('active');
            
            // Toggle hamburger to X
            const spans = mobileMenu.querySelectorAll('span');
            if(mobileMenu.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        });
        
        // Close mobile menu when clicking a link
        document.querySelectorAll('.mobile-nav a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.remove('active');
                mobileNav.classList.remove('active');
                
                // Reset hamburger icon
                const spans = mobileMenu.querySelectorAll('span');
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            });
        });

        // Post card hover effect
        document.querySelectorAll('.post-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
            });
        });
        
        // Newsletter form submission
        document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if(email) {
                alert(`Thank you for subscribing with ${email}! You'll receive our next newsletter soon.`);
                this.reset();
            }
        });
        // New JavaScript for comment functionality
        function toggleLike(button) {
            const likeBtn = button;
            const likeCount = likeBtn.querySelector('.like-count');
            let count = parseInt(likeCount.textContent);
            
            if (likeBtn.classList.contains('liked')) {
                likeBtn.classList.remove('liked');
                likeBtn.innerHTML = `<i class="far fa-thumbs-up"></i> <span class="like-count">${count - 1}</span>`;
            } else {
                likeBtn.classList.add('liked');
                likeBtn.innerHTML = `<i class="fas fa-thumbs-up"></i> <span class="like-count">${count + 1}</span>`;
            }
        }
        
        // Comment form submission
        document.querySelector('.comment-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('comment-name').value;
            const commentText = document.getElementById('comment-text').value;
            
            if (name && commentText) {
                alert('Thank you for your comment! It will be visible after moderation.');
                this.reset();
            } else {
                alert('Please fill in all required fields');
            }
        });
    </script>
</body>
</html>