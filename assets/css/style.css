:root {
    --primary: #2c5cc5;
    --primary-dark: #1a3d8f;
    --secondary: #4caf50;
    --accent: #ff9800;
    --dark: #2c3e50;
    --light: #f8f9fa;
    --gray: #6c757d;
    --white: #ffffff;
    --light-gray: #e9ecef;
    --shadow: 0 4px 12px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--light);
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
header {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 0;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
    text-decoration: none;
    z-index: 1001;
}

.logo i {
    margin-right: 0.5rem;
    color: var(--secondary);
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    transition: var(--transition);
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary);
}

.cta-button {
    background-color: var(--accent);
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
    text-decoration: none;
    display: inline-block;
}

.cta-button:hover {
    background-color: #e68a00;
    transform: translateY(-3px);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1001;
}

.menu-toggle span {
    height: 3px;
    width: 100%;
    background-color: var(--dark);
    border-radius: 3px;
    transition: var(--transition);
}

/* Mobile Menu Styles */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
    transform: translateY(-100%);
    transition: transform 0.5s ease;
}

.mobile-nav.active {
    transform: translateY(0);
}

.mobile-nav ul {
    list-style: none;
    text-align: center;
}

.mobile-nav ul li {
    margin: 1.5rem 0;
}

.mobile-nav ul li a {
    text-decoration: none;
    color: var(--dark);
    font-size: 1.5rem;
    font-weight: 500;
    transition: var(--transition);
}

.mobile-nav ul li a:hover {
    color: var(--primary);
}

.mobile-cta {
    margin-top: 2rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(to right, rgba(44, 92, 197, 0.85), rgba(26, 61, 143, 0.85)), url('https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1472&q=80');
    background-size: cover;
    background-position: center;
    color: var(--white);
    padding: 10rem 0 5rem;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2.5rem;
    padding: 5rem 0;
}

.blog-posts {
    flex: 1;
    min-width: 300px;
}

.sidebar {
    flex: 0 0 320px;
}

/* Post Card */
.post-card {
    background: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 2.5rem;
    transition: var(--transition);
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.post-image {
    height: 250px;
    background-color: #f0f0f0;
    overflow: hidden;
    position: relative;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.post-card:hover .post-image img {
    transform: scale(1.05);
}

.post-content {
    padding: 1.8rem;
}

.post-meta {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--gray);
    font-size: 0.9rem;
    flex-wrap: wrap;
}

.post-meta span {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
    margin-bottom: 0.5rem;
}

.post-meta i {
    margin-right: 0.5rem;
    color: var(--primary);
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.tag {
    background: #e8f4ff;
    color: var(--primary);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
    text-decoration: none;
}

.tag:hover {
    background: var(--primary);
    color: white;
}

.post-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-dark);
}

.post-title a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.post-title a:hover {
    color: var(--primary);
}

.post-excerpt {
    margin-bottom: 1.5rem;
    color: var(--dark);
}

.read-more {
    display: inline-flex;
    align-items: center;
    color: var(--primary);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.read-more i {
    margin-left: 0.5rem;
    transition: var(--transition);
}

.read-more:hover {
    color: var(--primary-dark);
}

.read-more:hover i {
    transform: translateX(5px);
}

/* Sidebar Styles */
.sidebar-widget {
    background: var(--white);
    border-radius: 8px;
    padding: 1.8rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.widget-title {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid var(--light-gray);
    color: var(--primary);
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--accent);
}

/* Search Widget */
.search-form {
    display: flex;
    margin-bottom: 1rem;
}

.search-form input {
    flex: 1;
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--light-gray);
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
    outline: none;
}

.search-form button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 0 1.2rem;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: var(--transition);
}

.search-form button:hover {
    background: var(--primary-dark);
}

/* Categories Widget */
.categories-list {
    list-style: none;
}

.categories-list li {
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    justify-content: space-between;
}

.categories-list li:last-child {
    border-bottom: none;
}

.categories-list a {
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}

.categories-list a:hover {
    color: var(--primary);
}

.category-count {
    background: var(--light-gray);
    color: var(--gray);
    padding: 0.2rem 0.6rem;
    border-radius: 20px;
    font-size: 0.85rem;
}

/* Popular Posts */
.popular-post {
    display: flex;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--light-gray);
}

.popular-post:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.popular-post-img {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

.popular-post-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-post-img i {
    font-size: 1.5rem;
    color: var(--gray);
}

.popular-post-content h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.popular-post-content h4 a {
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}

.popular-post-content h4 a:hover {
    color: var(--primary);
}

.popular-post-date {
    font-size: 0.85rem;
    color: var(--gray);
}

/* Newsletter Widget */
.newsletter-form input {
    width: 100%;
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--light-gray);
    border-radius: 4px;
    font-size: 1rem;
    margin-bottom: 1rem;
    outline: none;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pagination a,
.pagination span {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}

.pagination a:hover,
.pagination .current {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* Single Post Styles */
.single-post {
    background: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.single-post-image {
    height: 400px;
    background-color: #f0f0f0;
    overflow: hidden;
}

.single-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.single-post-content {
    padding: 2rem;
}

.single-post-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-dark);
}

.single-post-content .post-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.single-post-content .post-content h2,
.single-post-content .post-content h3,
.single-post-content .post-content h4 {
    margin: 2rem 0 1rem;
    color: var(--primary-dark);
}

.single-post-content .post-content p {
    margin-bottom: 1.5rem;
}

.single-post-content .post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 1rem 0;
}

/* Comments Section */
.comments-section {
    background: var(--white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.comments-section h3 {
    color: var(--primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid var(--light-gray);
}

.comment {
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--light-gray);
}

.comment:last-child {
    border-bottom: none;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-author {
    font-weight: 600;
    color: var(--primary);
    margin-right: 1rem;
}

.comment-date {
    color: var(--gray);
    font-size: 0.9rem;
}

.comment-content {
    color: var(--dark);
    line-height: 1.6;
}

/* Comment Form */
.comment-form {
    background: var(--white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.comment-form h3 {
    color: var(--primary);
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--light-gray);
    border-radius: 4px;
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
footer {
    background-color: var(--dark);
    color: var(--white);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--accent);
}

.footer-section p {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
    transition: var(--transition);
}

.footer-links a:hover {
    opacity: 1;
    color: var(--accent);
}

.social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icons a {
    width: 40px;
    height: 40px;
    background: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-icons a:hover {
    background: var(--accent);
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .menu-toggle {
        display: flex;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .main-content {
        flex-direction: column;
        padding: 3rem 0;
    }

    .sidebar {
        flex: none;
    }

    .single-post-content h1 {
        font-size: 2rem;
    }

    .post-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .post-meta span {
        margin-right: 0;
    }
}

@media (max-width: 480px) {
    .container {
        width: 95%;
    }

    .hero {
        padding: 8rem 0 3rem;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .post-card,
    .sidebar-widget,
    .single-post,
    .comments-section,
    .comment-form {
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 0;
    }

    .pagination {
        gap: 0.25rem;
    }

    .pagination a,
    .pagination span {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-primary {
    color: var(--primary);
}

.text-gray {
    color: var(--gray);
}

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

.btn {
    display: inline-block;
    padding: 0.7rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--gray);
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}
