<?php
require_once 'includes/functions.php';

// Page settings
$page_title = 'All Categories';
$page_description = 'Browse all categories and discover content that interests you.';
$page_keywords = 'categories, blog topics, content organization';

// Get all categories
$categories = getCategories();

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>Browse Categories</h1>
        <p>Discover content organized by topics that interest you most.</p>
    </div>
</section>

<!-- Main Content -->
<section class="main-content container">
    <div class="categories-grid">
        <?php if (empty($categories)): ?>
            <div class="no-categories">
                <h2>No categories found</h2>
                <p>Categories will appear here once they are created.</p>
            </div>
        <?php else: ?>
            <?php foreach ($categories as $category): ?>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="category-content">
                        <h3>
                            <a href="category.php?slug=<?php echo $category['slug']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        </h3>
                        <p class="category-description">
                            <?php echo $category['description'] ? htmlspecialchars($category['description']) : 'Explore posts in this category.'; ?>
                        </p>
                        <div class="category-meta">
                            <span class="post-count">
                                <i class="fas fa-file-alt"></i>
                                <?php echo $category['post_count']; ?> 
                                <?php echo $category['post_count'] == 1 ? 'post' : 'posts'; ?>
                            </span>
                            <a href="category.php?slug=<?php echo $category['slug']; ?>" class="view-category">
                                View Posts <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Popular Categories Section -->
    <?php if (!empty($categories)): ?>
        <div class="popular-categories">
            <h2>Most Popular Categories</h2>
            <div class="popular-categories-list">
                <?php
                // Sort categories by post count
                usort($categories, function($a, $b) {
                    return $b['post_count'] - $a['post_count'];
                });
                
                $top_categories = array_slice($categories, 0, 5);
                ?>
                <?php foreach ($top_categories as $index => $category): ?>
                    <div class="popular-category-item">
                        <div class="rank"><?php echo $index + 1; ?></div>
                        <div class="category-info">
                            <h4>
                                <a href="category.php?slug=<?php echo $category['slug']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                            </h4>
                            <span class="post-count"><?php echo $category['post_count']; ?> posts</span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</section>

<style>
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.category-card {
    background: var(--white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: center;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 2rem;
}

.category-content h3 {
    margin-bottom: 1rem;
    color: var(--primary-dark);
}

.category-content h3 a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.category-content h3 a:hover {
    color: var(--primary);
}

.category-description {
    color: var(--gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.category-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--light-gray);
}

.post-count {
    color: var(--gray);
    font-size: 0.9rem;
}

.view-category {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.view-category:hover {
    color: var(--primary-dark);
}

.view-category i {
    margin-left: 0.5rem;
    transition: var(--transition);
}

.view-category:hover i {
    transform: translateX(3px);
}

.popular-categories {
    background: var(--white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.popular-categories h2 {
    color: var(--primary-dark);
    margin-bottom: 1.5rem;
    text-align: center;
}

.popular-categories-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.popular-category-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--light);
    border-radius: 6px;
    transition: var(--transition);
}

.popular-category-item:hover {
    background: #e8f4ff;
}

.rank {
    width: 40px;
    height: 40px;
    background: var(--primary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
}

.category-info {
    flex: 1;
}

.category-info h4 {
    margin-bottom: 0.25rem;
}

.category-info h4 a {
    text-decoration: none;
    color: var(--dark);
    transition: var(--transition);
}

.category-info h4 a:hover {
    color: var(--primary);
}

.category-info .post-count {
    font-size: 0.8rem;
    color: var(--gray);
}

.no-categories {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    background: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.no-categories h2 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

.no-categories p {
    color: var(--gray);
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .category-card {
        padding: 1.5rem;
    }
    
    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .category-meta {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .popular-category-item {
        padding: 0.8rem;
    }
    
    .rank {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
