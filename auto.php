<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Webhook API Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #212529;
            padding: 2rem;
            line-height: 1.6;
        }

        h1,
        h2,
        h3 {
            color: #343a40;
        }

        code,
        pre {
            background: #ffffff;
            color: #000000;
            font-family: Consolas, monospace;
            font-size: 14px;
            padding: 0.5em;
            border-radius: 6px;
            white-space: pre-wrap;
            /* ✅ allow wrapping */
            word-wrap: break-word;
            /* ✅ break long words */
            overflow-x: hidden;
            /* ✅ prevent horizontal scroll */
            display: block;
            /* ensures proper rendering */
        }

        pre {
            margin: 1em 0;
        }

        pre {
            background-color: #ffffff;
            color: #000000;
            padding: 1rem;
            overflow-x: auto;
            border-radius: 8px;
        }
/* 
        code {
            background-color: #ffffff;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: monospace;
        } */

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: #fff;
        }

        th,
        td {
            padding: 0.75rem;
            border: 1px solid #dee2e6;
            text-align: left;
        }

        th {
            background-color: #f1f1f1;
        }

        .section {
            margin-bottom: 2rem;
            background: #ffffff;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
        }

        hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 2rem 0;
        }
    </style>
</head>

<body>

    <h1>📘 Webhook Authentication & Blog Post Creation API Documentation</h1>

    <div class="section">
        <h2>🔗 Webhook URL Format</h2>
        <div style="max-width: 100%; word-wrap: break-word;">
        <pre><code>POST http://localhost/your-folder/hhsjfbbshbfsgsgefgu.php?ggg=efhkshkjshrrgh&amp;isif=734-bdfd-7sbf3-vdv01&amp;qwen=023987576892357734-7583</code></pre>
    </div>
    </div>

    <div class="section">
        <h2>📥 Required POST Data (JSON)</h2>

        <h3>✅ Minimum Required Fields:</h3>
        <div style="max-width: 100%; word-wrap: break-word;">
        <pre><code>{
  "title": "Your Blog Post Title",
  "content": "Your full blog post content here..."
}</code></pre>
        </div>
        <h3>🧿 Complete Example with All Options:</h3>
        <div style="max-width: 100%; word-wrap: break-word;">
        <pre><code>{
  "title": "Amazing Blog Post Title",
  "content": "This is the full content of your blog post. It can be very long and include HTML formatting if needed.",
  "excerpt": "Brief description of the post (optional - auto-generated if not provided)",
  "category": "technology",
  "tags": ["web development", "php", "automation"],
  "featured_image": "https://example.com/image.jpg",
  "status": "published",
  "author_id": 1,
  "slug": "custom-url-slug",
  "meta_description": "SEO meta description",
  "meta_keywords": "seo, keywords, blog"
}</code></pre>
        </div>
    </div><div class="section">
      <h2>🧾 Field Descriptions</h2>
      <table>
          <thead>
              <tr>
                  <th>Field</th>
                  <th>Type</th>
                  <th>Required</th>
                  <th>Description</th>
              </tr>
          </thead>
          <tbody>
              <tr>
                  <td><code>title</code></td>
                  <td>string</td>
                  <td>✅ Yes</td>
                  <td>Blog post title</td>
              </tr>
              <tr>
                  <td><code>content</code></td>
                  <td>string</td>
                  <td>✅ Yes</td>
                  <td>Full blog post content (can include HTML)</td>
              </tr>
              <tr>
                  <td><code>excerpt</code></td>
                  <td>string</td>
                  <td>❌ No</td>
                  <td>Brief description (auto-generated if empty)</td>
              </tr>
              <tr>
                  <td><code>category</code></td>
                  <td>string</td>
                  <td>❌ No</td>
                  <td>Category slug (creates category if doesn't exist)</td>
              </tr>
              <tr>
                  <td><code>tags</code></td>
                  <td>array</td>
                  <td>❌ No</td>
                  <td>Array of tag names</td>
              </tr>
              <tr>
                  <td><code>featured_image</code></td>
                  <td>string</td>
                  <td>❌ No</td>
                  <td>URL to featured image</td>
              </tr>
              <tr>
                  <td><code>status</code></td>
                  <td>string</td>
                  <td>❌ No</td>
                  <td>"published" or "draft" (default: "published")</td>
              </tr>
              <tr>
                  <td><code>author_id</code></td>
                  <td>integer</td>
                  <td>❌ No</td>
                  <td>User ID (default: 1 - admin)</td>
              </tr>
              <tr>
                  <td><code>slug</code></td>
                  <td>string</td>
                  <td>❌ No</td>
                  <td>Custom URL slug (auto-generated if empty)</td>
              </tr>
              <tr>
                  <td><code>meta_description</code></td>
                  <td>string</td>
                  <td>❌ No</td>
                  <td>SEO meta description</td>
              </tr>
              <tr>
                  <td><code>meta_keywords</code></td>
                  <td>string</td>
                  <td>❌ No</td>
                  <td>SEO keywords</td>
              </tr>
          </tbody>
      </table>
  </div>

  <div class="section">
      <h2>⚙️ n8n Webhook Configuration</h2>
      <h3>1. HTTP Request Node Settings:</h3>
      <ul>
          <li><strong>Method:</strong> POST</li>
          <li><strong>URL:</strong></li>
      </ul>
      <div style="max-width: 100%; word-wrap: break-word;">
      <pre><code>http://your-domain/hhsjfbbshbfsgsgefgu.php?ggg=efhkshkjshrrgh&amp;isif=734-bdfd-7sbf3-vdv01&amp;qwen=023987576892357734-7583</code></pre>
  </div>
      <h3>Headers:</h3>
      <pre><code>Content-Type: application/json</code></pre>

      <h3>2. Example n8n JSON Body:</h3>
      <div style="max-width: 100%; word-wrap: break-word;">
      <pre><code>{
"title": "{{ $json.title }}",
"content": "{{ $json.content }}",
"excerpt": "{{ $json.excerpt }}",
"category": "{{ $json.category }}",
"tags": {{ $json.tags }},
"featured_image": "{{ $json.featured_image }}",
"status": "published"
}</code></pre></div>
  </div><div class="section">
    <h2>📝 Response Format</h2>
    <h3>✅ Success Response (201):</h3>
    <div style="max-width: 100%; word-wrap: break-word;">
    <pre><code>{
"success": true,
"message": "Blog post created successfully",
"data": {
"post_id": 123,
"title": "Your Post Title",
"slug": "your-post-title",
"status": "published",
"category": {
  "id": 1,
  "name": "Technology",
  "slug": "technology"
},
"author": {
  "id": 1,
  "name": "admin"
},
"tags": [
  {"name": "web development", "slug": "web-development"},
  {"name": "php", "slug": "php"}
],
"featured_image": "https://example.com/image.jpg",
"excerpt": "Brief description...",
"created_at": "2024-01-15 10:30:00",
"post_url": "http://your-domain/post.php?slug=your-post-title"
},
"timestamp": "2024-01-15 10:30:00"
}</code></pre>
    </div>

    <h3>❌ Error Response (400/401/500):</h3>
    <div style="max-width: 100%; word-wrap: break-word;">
    <pre><code>{
"success": false,
"message": "Error description",
"data": null,
"timestamp": "2024-01-15 10:30:00"
}</code></pre></div>
</div>

<div class="section">
    <h2>🔐 Security Features</h2>
    <ul>
        <li><strong>Authentication:</strong> Three-parameter validation</li>
        <li><strong>Method Restriction:</strong> Only POST requests allowed</li>
        <li><strong>Input Validation:</strong> Checks all required fields</li>
        <li><strong>SQL Injection Protection:</strong> Uses prepared statements</li>
        <li><strong>Logging:</strong> All requests logged to <code>webhook_log.txt</code></li>
        <li><strong>Error Handling:</strong> Detailed error messages</li>
    </ul>
</div>

<div class="section">
    <h2>🧠 Smart Features</h2>
    <ul>
        <li>Auto-slug generation</li>
        <li>Handles duplicate slugs</li>
        <li>Auto-excerpt if not provided</li>
        <li>Creates categories automatically</li>
        <li>Creates and links tags automatically</li>
        <li>Defaults to admin if no author found</li>
    </ul>
</div>

<div class="section">
    <h2>🧪 Testing the Webhook</h2>
    <p>Use this cURL command to test the webhook:</p>
    <div style="max-width: 100%; word-wrap: break-word;">
    <pre><code>curl -X POST "http://localhost/your-folder/hhsjfbbshbfsgsgefgu.php?ggg=efhkshkjshrrgh&amp;isif=734-bdfd-7sbf3-vdv01&amp;qwen=023987576892357734-7583" \
-H "Content-Type: application/json" \
-d '{
"title": "Test Post from Automation",
"content": "This is a test post created via webhook automation.",
"category": "automation",
"tags": ["test", "webhook", "automation"]
}'</code></pre></div>
</div>

<div class="section">
    <h2>📋 Error Codes</h2>
    <table>
        <thead>
            <tr>
                <th>Code</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>200</td>
                <td>Success</td>
            </tr>
            <tr>
                <td>400</td>
                <td>Bad Request (missing fields, invalid JSON)</td>
            </tr>
            <tr>
                <td>401</td>
                <td>Unauthorized (authentication failed)</td>
            </tr>
            <tr>
                <td>405</td>
                <td>Method Not Allowed (only POST allowed)</td>
            </tr>
            <tr>
                <td>500</td>
                <td>Server Error (e.g., database issues)</td>
            </tr>
        </tbody>
    </table>
</div>

<p><strong>🎉 Your webhook is now ready for n8n automation!</strong><br>
    It will securely create blog posts with all the features you need. 🚀</p>

</body>

</html>