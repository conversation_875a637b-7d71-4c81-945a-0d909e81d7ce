 // Mobile Menu Toggle
 const mobileMenu = document.getElementById('mobile-menu');
 const mobileNav = document.getElementById('mobile-nav');
 
 mobileMenu.addEventListener('click', function() {
     mobileMenu.classList.toggle('active');
     mobileNav.classList.toggle('active');
     
     // Toggle hamburger to X
     const spans = mobileMenu.querySelectorAll('span');
     if(mobileMenu.classList.contains('active')) {
         spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
         spans[1].style.opacity = '0';
         spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
     } else {
         spans[0].style.transform = 'none';
         spans[1].style.opacity = '1';
         spans[2].style.transform = 'none';
     }
 });
 
 // Close mobile menu when clicking a link
 document.querySelectorAll('.mobile-nav a').forEach(link => {
     link.addEventListener('click', () => {
         mobileMenu.classList.remove('active');
         mobileNav.classList.remove('active');
         
         // Reset hamburger icon
         const spans = mobileMenu.querySelectorAll('span');
         spans[0].style.transform = 'none';
         spans[1].style.opacity = '1';
         spans[2].style.transform = 'none';
     });
 });

 // Pagination active state
 document.querySelectorAll('.pagination button').forEach(button => {
     button.addEventListener('click', function() {
         document.querySelector('.pagination button.active').classList.remove('active');
         this.classList.add('active');
     });
 });

 // Book card hover effect
 document.querySelectorAll('.book-card').forEach(card => {
     card.addEventListener('mouseenter', function() {
         this.style.transform = 'translateY(-10px)';
         this.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1)';
     });
     
     card.addEventListener('mouseleave', function() {
         this.style.transform = 'translateY(0)';
         this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
     });
 });