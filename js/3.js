 // Mobile Menu Toggle
 const mobileMenu = document.getElementById('mobile-menu');
 const mobileNav = document.getElementById('mobile-nav');
 
 mobileMenu.addEventListener('click', function() {
     mobileMenu.classList.toggle('active');
     mobileNav.classList.toggle('active');
     
     // Toggle hamburger to X
     const spans = mobileMenu.querySelectorAll('span');
     if(mobileMenu.classList.contains('active')) {
         spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
         spans[1].style.opacity = '0';
         spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
     } else {
         spans[0].style.transform = 'none';
         spans[1].style.opacity = '1';
         spans[2].style.transform = 'none';
     }
 });
 
 // Close mobile menu when clicking a link
 document.querySelectorAll('.mobile-nav a').forEach(link => {
     link.addEventListener('click', () => {
         mobileMenu.classList.remove('active');
         mobileNav.classList.remove('active');
         
         // Reset hamburger icon
         const spans = mobileMenu.querySelectorAll('span');
         spans[0].style.transform = 'none';
         spans[1].style.opacity = '1';
         spans[2].style.transform = 'none';
     });
 });

 // FAQ Accordion
 document.querySelectorAll('.faq-question').forEach(question => {
     question.addEventListener('click', () => {
         const faqItem = question.parentElement;
         faqItem.classList.toggle('active');
     });
 });

 // Form Submission
 document.getElementById('rental-form').addEventListener('submit', function(e) {
     e.preventDefault();
     alert('Thank you for your request! Our team will contact you shortly to arrange your demo rental.');
     this.reset();
 });

 // Smooth Scrolling for Navigation
 document.querySelectorAll('a[href^="#"]').forEach(anchor => {
     anchor.addEventListener('click', function (e) {
         e.preventDefault();
         document.querySelector(this.getAttribute('href')).scrollIntoView({
             behavior: 'smooth'
         });
     });
 });

 // Sticky Header
 window.addEventListener('scroll', function() {
     const header = document.querySelector('header');
     header.classList.toggle('sticky', window.scrollY > 0);
 });