<?php
// Simple test file to check if everything is working
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Blog Application Test</h1>";

// Test 1: PHP Version
echo "<h2>1. PHP Version Check</h2>";
echo "PHP Version: " . phpversion() . "<br>";
if (version_compare(phpversion(), '7.4.0', '>=')) {
    echo "<span style='color: green;'>✓ PHP version is compatible</span><br>";
} else {
    echo "<span style='color: red;'>✗ PHP version is too old. Please upgrade to 7.4+</span><br>";
}

// Test 2: Required Extensions
echo "<h2>2. Required Extensions</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span style='color: green;'>✓ $ext extension is loaded</span><br>";
    } else {
        echo "<span style='color: red;'>✗ $ext extension is missing</span><br>";
    }
}

// Test 3: Database Connection
echo "<h2>3. Database Connection Test</h2>";
try {
    // Test basic MySQL connection
    $host = 'localhost';
    $user = 'root';
    $pass = '';
    
    $pdo_test = new PDO("mysql:host=$host", $user, $pass);
    $pdo_test->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<span style='color: green;'>✓ MySQL connection successful</span><br>";
    
    // Test database creation
    $pdo_test->exec("CREATE DATABASE IF NOT EXISTS blog_app");
    echo "<span style='color: green;'>✓ Database 'blog_app' created/exists</span><br>";
    
    // Test connection to specific database
    $pdo_blog = new PDO("mysql:host=$host;dbname=blog_app", $user, $pass);
    $pdo_blog->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<span style='color: green;'>✓ Connection to blog_app database successful</span><br>";
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
    echo "<p><strong>Possible solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP is running</li>";
    echo "<li>Start MySQL service in XAMPP Control Panel</li>";
    echo "<li>Check if port 3306 is available</li>";
    echo "<li>Verify MySQL credentials (default: root with no password)</li>";
    echo "</ul>";
}

// Test 4: File Permissions
echo "<h2>4. File Permissions Test</h2>";
$test_dirs = ['config', 'includes', 'assets', 'admin'];
foreach ($test_dirs as $dir) {
    if (is_dir($dir)) {
        if (is_readable($dir)) {
            echo "<span style='color: green;'>✓ Directory '$dir' is readable</span><br>";
        } else {
            echo "<span style='color: red;'>✗ Directory '$dir' is not readable</span><br>";
        }
    } else {
        echo "<span style='color: orange;'>⚠ Directory '$dir' does not exist</span><br>";
    }
}

// Test 5: Include Files
echo "<h2>5. Include Files Test</h2>";
$include_files = [
    'config/database.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($include_files as $file) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✓ File '$file' exists</span><br>";
    } else {
        echo "<span style='color: red;'>✗ File '$file' is missing</span><br>";
    }
}

// Test 6: Try to include the main files
echo "<h2>6. File Include Test</h2>";
try {
    if (file_exists('config/database.php')) {
        include_once 'config/database.php';
        echo "<span style='color: green;'>✓ Database configuration loaded successfully</span><br>";
    }
    
    if (file_exists('includes/functions.php')) {
        include_once 'includes/functions.php';
        echo "<span style='color: green;'>✓ Functions file loaded successfully</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Error loading files: " . $e->getMessage() . "</span><br>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If all tests pass, try accessing:</p>";
echo "<ul>";
echo "<li><a href='index.php'>Main Blog (index.php)</a></li>";
echo "<li><a href='admin/login.php'>Admin Panel (admin/login.php)</a></li>";
echo "<li><a href='install_sample_data.php'>Install Sample Data</a></li>";
echo "</ul>";

echo "<h2>Troubleshooting</h2>";
echo "<p>If you're still getting errors:</p>";
echo "<ol>";
echo "<li>Check XAMPP Control Panel - ensure Apache and MySQL are running (green status)</li>";
echo "<li>Try restarting XAMPP services</li>";
echo "<li>Check if any antivirus is blocking the connection</li>";
echo "<li>Verify the files are in the correct htdocs folder</li>";
echo "<li>Check Apache error logs in XAMPP</li>";
echo "</ol>";
?>
