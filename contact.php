<?php
require_once 'includes/functions.php';

// Page settings
$page_title = 'Contact Us';
$page_description = 'Get in touch with Creative Blog. We\'d love to hear from you!';
$page_keywords = 'contact, get in touch, feedback, collaboration';

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $subject = sanitize($_POST['subject']);
    $contact_message = sanitize($_POST['message']);
    
    if (!empty($name) && !empty($email) && !empty($subject) && !empty($contact_message)) {
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            // In a real application, you would send an email here
            // For now, we'll just show a success message
            $message = "Thank you for your message! We'll get back to you soon.";
            
            // You could also save the message to a database table
            // or send it via email using P<PERSON>'s mail() function or a service like <PERSON><PERSON><PERSON><PERSON><PERSON>
            
        } else {
            $error = "Please enter a valid email address.";
        }
    } else {
        $error = "All fields are required.";
    }
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>Contact Us</h1>
        <p>We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
    </div>
</section>

<!-- Main Content -->
<section class="main-content container">
    <div class="contact-content">
        <div class="contact-form-section">
            <div class="contact-form-container">
                <h2>Send us a Message</h2>
                
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="contact-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Your Name *</label>
                            <input type="text" id="name" name="name" required 
                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                                   placeholder="Enter your full name">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required 
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                   placeholder="Enter your email address">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="subject">Subject *</label>
                        <select id="subject" name="subject" required>
                            <option value="">Select a subject</option>
                            <option value="General Inquiry" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'General Inquiry') ? 'selected' : ''; ?>>General Inquiry</option>
                            <option value="Collaboration" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Collaboration') ? 'selected' : ''; ?>>Collaboration</option>
                            <option value="Guest Post" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Guest Post') ? 'selected' : ''; ?>>Guest Post Submission</option>
                            <option value="Technical Issue" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Technical Issue') ? 'selected' : ''; ?>>Technical Issue</option>
                            <option value="Feedback" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Feedback') ? 'selected' : ''; ?>>Feedback</option>
                            <option value="Other" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Other') ? 'selected' : ''; ?>>Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Message *</label>
                        <textarea id="message" name="message" rows="6" required 
                                  placeholder="Tell us how we can help you..."><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Message
                    </button>
                </form>
            </div>
        </div>
        
        <div class="contact-info-section">
            <div class="contact-info">
                <h2>Get in Touch</h2>
                <p>Have questions, suggestions, or just want to say hello? We're here to help and would love to hear from you.</p>
                
                <div class="contact-methods">
                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="method-content">
                            <h3>Email Us</h3>
                            <p><EMAIL></p>
                            <small>We typically respond within 24 hours</small>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="method-content">
                            <h3>Response Time</h3>
                            <p>24-48 hours</p>
                            <small>Monday to Friday, 9 AM - 6 PM</small>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="method-content">
                            <h3>Follow Us</h3>
                            <div class="social-links">
                                <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                                <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                                <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                            <small>Stay updated with our latest posts</small>
                        </div>
                    </div>
                </div>
                
                <div class="faq-section">
                    <h3>Frequently Asked Questions</h3>
                    <div class="faq-item">
                        <h4>Can I submit a guest post?</h4>
                        <p>Yes! We welcome guest contributions. Please use the contact form with "Guest Post Submission" as the subject.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h4>How can I collaborate with you?</h4>
                        <p>We're always open to collaborations. Reach out to us with your ideas and we'll discuss the possibilities.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h4>Do you accept advertising?</h4>
                        <p>We consider advertising partnerships that align with our values and provide value to our readers.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-form-container {
    background: var(--white);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.contact-form-container h2 {
    color: var(--primary-dark);
    margin-bottom: 1.5rem;
}

.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.contact-form {
    margin-top: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid var(--light-gray);
    border-radius: 4px;
    font-size: 1rem;
    transition: var(--transition);
    outline: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(44, 92, 197, 0.1);
}

.contact-info {
    background: var(--white);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.contact-info h2 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

.contact-info > p {
    color: var(--gray);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-methods {
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--light-gray);
}

.contact-method:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.method-icon {
    width: 50px;
    height: 50px;
    background: var(--primary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.method-content h3 {
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.method-content p {
    color: var(--dark);
    margin-bottom: 0.25rem;
}

.method-content small {
    color: var(--gray);
    font-size: 0.8rem;
}

.social-links {
    display: flex;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.social-links a {
    width: 35px;
    height: 35px;
    background: var(--light-gray);
    color: var(--gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--primary);
    color: var(--white);
}

.faq-section {
    border-top: 1px solid var(--light-gray);
    padding-top: 2rem;
}

.faq-section h3 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

.faq-item {
    margin-bottom: 1.5rem;
}

.faq-item h4 {
    color: var(--dark);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.faq-item p {
    color: var(--gray);
    line-height: 1.6;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .contact-form-container,
    .contact-info {
        padding: 1.5rem;
    }
    
    .contact-method {
        flex-direction: column;
        text-align: center;
    }
    
    .method-icon {
        margin: 0 auto 1rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
